#!/usr/bin/env python3
"""
Test both working endpoints for load balancing
"""

import requests
import time

# Test both endpoints
endpoints = [
    {
        'name': 'GPT-4o-Secondary',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACYeBjFXJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    {
        'name': 'GPT-4.1-Secondary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BCACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1'
    }
]

def test_endpoint(endpoint_config, test_num):
    """Test a single endpoint"""
    print(f"\n🧪 Test {test_num}: {endpoint_config['name']}")
    
    try:
        start_time = time.time()
        
        headers = {
            'Content-Type': 'application/json',
            'api-key': endpoint_config['api_key']
        }
        
        payload = {
            "model": endpoint_config['model'],
            "messages": [
                {
                    "role": "user",
                    "content": f"Respond with: {endpoint_config['name']} working"
                }
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        response = requests.post(
            endpoint_config['endpoint'],
            headers=headers,
            json=payload,
            timeout=15
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            content = response.json()['choices'][0]['message']['content']
            print(f"   ✅ Success: {content}")
            print(f"   ⏱️ Response time: {response_time:.2f}s")
            return True, response_time
        else:
            print(f"   ❌ Failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False, response_time
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False, 0

def main():
    print("🔄 Testing Dual Endpoint Load Balancing")
    print("=" * 50)
    
    working_endpoints = []
    total_tests = 6
    
    # Test alternating between endpoints (simulating load balancing)
    for i in range(total_tests):
        endpoint_index = i % len(endpoints)
        endpoint = endpoints[endpoint_index]
        
        success, response_time = test_endpoint(endpoint, i + 1)
        
        if success:
            working_endpoints.append({
                'name': endpoint['name'],
                'response_time': response_time
            })
        
        # Small delay between requests
        time.sleep(0.5)
    
    print("\n" + "=" * 50)
    print("📊 LOAD BALANCING RESULTS")
    print("=" * 50)
    
    if len(working_endpoints) > 0:
        print(f"✅ Successful requests: {len(working_endpoints)}/{total_tests}")
        
        # Group by endpoint
        gpt4o_times = [e['response_time'] for e in working_endpoints if 'GPT-4o' in e['name']]
        gpt41_times = [e['response_time'] for e in working_endpoints if 'GPT-4.1' in e['name']]
        
        if gpt4o_times:
            avg_gpt4o = sum(gpt4o_times) / len(gpt4o_times)
            print(f"📈 GPT-4o average response time: {avg_gpt4o:.2f}s ({len(gpt4o_times)} requests)")
        
        if gpt41_times:
            avg_gpt41 = sum(gpt41_times) / len(gpt41_times)
            print(f"📈 GPT-4.1 average response time: {avg_gpt41:.2f}s ({len(gpt41_times)} requests)")
        
        if len(working_endpoints) == total_tests:
            print("\n🎉 PERFECT! Both endpoints working consistently")
            print("✅ Load balancing will provide:")
            print("   - Faster response times")
            print("   - Better reliability")
            print("   - Higher concurrent capacity")
        else:
            print(f"\n⚠️ Some requests failed ({total_tests - len(working_endpoints)} failures)")
            print("Load balancing will still work but with reduced reliability")
    else:
        print("❌ No successful requests - both endpoints failing")
    
    return len(working_endpoints) == total_tests

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 RECOMMENDATION: Use both endpoints for optimal performance")
        print("The Process B system now has:")
        print("  ✅ 2 working endpoints")
        print("  ✅ Load balancing")
        print("  ✅ Redundancy")
        print("  ✅ Faster processing")
    else:
        print(f"\n⚠️ RECOMMENDATION: Monitor endpoint stability")
        print("Some endpoints may be intermittent")
