<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technical Drawing Analyzer by <PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/static/images/manu-logo.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .text-manu-red {
            color: #e30613;
        }
        .bg-manu-red {
            background-color: #e30613;
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .navbar-brand {
            font-weight: 600;
            color: #e30613;
        }
        .navbar-brand img {
            height: 40px;
            margin-right: 10px;
        }
        .logo-text {
            vertical-align: middle;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .upload-area {
            border: 2px dashed #e30613;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background-color: rgba(227, 6, 19, 0.05);
        }
        .upload-icon {
            font-size: 48px;
            color: #e30613;
            margin-bottom: 15px;
        }
        .btn-primary {
            background-color: #e30613;
            border-color: #e30613;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #c00510;
            border-color: #c00510;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(227, 6, 19, 0.2);
        }
        .model-selector {
            margin-top: 20px;
        }
        .model-btn {
            border: 1px solid #dee2e6;
            background-color: white;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        .model-btn.active {
            background-color: #e30613;
            color: white;
            border-color: #e30613;
        }
        .model-btn:hover:not(.active) {
            background-color: #f8f9fa;
        }
        .dropdown-container {
            margin-bottom: 15px;
        }
        .form-select {
            border-radius: 8px;
            padding: 10px;
            border: 1px solid #dee2e6;
        }
        .form-select:focus {
            border-color: #e30613;
            box-shadow: 0 0 0 0.25rem rgba(227, 6, 19, 0.25);
        }
        .create-template-btn {
            display: none;
            margin-top: 10px;
        }
        .process-selector {
            margin-bottom: 20px;
        }
        .process-option {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            align-items: center;
        }
        .process-option:hover {
            border-color: #e30613;
            background-color: rgba(227, 6, 19, 0.05);
        }
        .process-option.active {
            border-color: #e30613;
            background-color: rgba(227, 6, 19, 0.1);
        }
        .process-icon {
            margin-right: 15px;
            font-size: 24px;
        }
        .process-info h6 {
            margin-bottom: 5px;
            font-weight: 600;
        }
        .process-info p {
            margin-bottom: 0;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease forwards;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3">
        <div class="container">
            <a class="navbar-brand" href="/">
                <div class="manu-logo"></div>
                <span class="logo-text">Technical Drawing Analyzer by Manu Yantralaya</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/"><i class="bi bi-house"></i> Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/static/index.html"><i class="bi bi-upload"></i> Upload</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="bi bi-info-circle"></i> About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <h1 class="text-center mb-2 text-manu-red">Technical Drawing Analyzer</h1>
        <p class="text-center text-muted mb-5">Analyze technical drawings with AI</p>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card p-4">
                    <form id="uploadForm">
                        <!-- Company Dropdown -->
                        <div class="dropdown-container">
                            <label for="companySelect" class="form-label">Company</label>
                            <select class="form-select" id="companySelect" required>
                                <option value="" selected disabled>Select a company</option>
                                <!-- Options will be populated via JavaScript -->
                            </select>
                        </div>

                        <!-- Cage Type Dropdown -->
                        <div class="dropdown-container">
                            <label for="cageTypeSelect" class="form-label">Cage Type</label>
                            <select class="form-select" id="cageTypeSelect" required disabled>
                                <option value="" selected disabled>Select a cage type</option>
                                <!-- Options will be populated via JavaScript -->
                            </select>
                        </div>

                        <!-- Template Dropdown -->
                        <div class="dropdown-container">
                            <label for="templateSelect" class="form-label">Template</label>
                            <select class="form-select" id="templateSelect" disabled>
                                <option value="" selected disabled>Select a template</option>
                                <!-- Options will be populated via JavaScript -->
                            </select>
                            <button type="button" id="createTemplateBtn" class="btn btn-outline-primary create-template-btn">
                                Create New Template
                            </button>
                        </div>

                        <div class="upload-area mt-4" id="dropArea">
                            <input type="file" id="fileInput" accept="image/jpeg, image/png, image/gif" style="display: none;">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-arrow-up"></i>
                            </div>
                            <p>Drag & drop an image here or click to browse</p>
                            <p class="text-muted small">Supported formats: JPG, PNG, GIF</p>
                        </div>

                        <!-- Analysis Process Selection -->
                        <div class="process-selector mt-4">
                            <label class="form-label">Analysis Process</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="process-option active" data-process="A">
                                        <div class="process-icon">
                                            <i class="bi bi-robot text-danger"></i>
                                        </div>
                                        <div class="process-info">
                                            <h6>Process A</h6>
                                            <p class="small text-muted">Automatic AI Analysis</p>
                                            <p class="small">AI detects all symbols automatically, then you can make corrections</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="process-option" data-process="B">
                                        <div class="process-icon">
                                            <i class="bi bi-check-circle text-success"></i>
                                        </div>
                                        <div class="process-info">
                                            <h6>Process B</h6>
                                            <p class="small text-muted">Manual Symbol Detection</p>
                                            <p class="small">You confirm each symbol one-by-one for maximum accuracy</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="model-selector">
                            <label class="form-label">AI Model</label>
                            <div class="d-flex justify-content-between">
                                <button type="button" class="model-btn flex-grow-1 me-2" data-model="claude">
                                    <i class="bi bi-cpu"></i> Claude
                                </button>
                                <button type="button" class="model-btn flex-grow-1 me-2" data-model="gpt-4o">
                                    <i class="bi bi-cpu"></i> GPT-4o
                                </button>
                                <button type="button" class="model-btn flex-grow-1 active" data-model="gpt-4.1">
                                    <i class="bi bi-cpu"></i> GPT-4.1
                                </button>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mt-4" id="processBtn" disabled>
                            <i class="bi bi-gear"></i> Process Image
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const dropArea = document.getElementById('dropArea');
            const fileInput = document.getElementById('fileInput');
            const processBtn = document.getElementById('processBtn');
            const modelBtns = document.querySelectorAll('.model-btn');
            const companySelect = document.getElementById('companySelect');
            const cageTypeSelect = document.getElementById('cageTypeSelect');
            const templateSelect = document.getElementById('templateSelect');
            const createTemplateBtn = document.getElementById('createTemplateBtn');

            let selectedFile = null;
            let selectedModel = 'gpt-4.1';

            // Fetch companies
            fetch('/api/companies/')
                .then(response => response.json())
                .then(data => {
                    data.forEach(company => {
                        const option = document.createElement('option');
                        option.value = company.id;
                        option.textContent = company.name;
                        companySelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error fetching companies:', error));

            // Company change event
            companySelect.addEventListener('change', function() {
                const companyId = this.value;

                // Clear and disable cage type and template selects
                cageTypeSelect.innerHTML = '<option value="" selected disabled>Select a cage type</option>';
                templateSelect.innerHTML = '<option value="" selected disabled>Select a template</option>';
                templateSelect.disabled = true;
                createTemplateBtn.style.display = 'none';

                if (companyId) {
                    cageTypeSelect.disabled = false;

                    // Fetch cage types for selected company
                    fetch(`/api/cage-types/?company_id=${companyId}`)
                        .then(response => response.json())
                        .then(data => {
                            data.forEach(cageType => {
                                const option = document.createElement('option');
                                option.value = cageType.id;
                                option.textContent = cageType.name;
                                cageTypeSelect.appendChild(option);
                            });
                        })
                        .catch(error => console.error('Error fetching cage types:', error));
                } else {
                    cageTypeSelect.disabled = true;
                }
            });

            // Cage type change event
            cageTypeSelect.addEventListener('change', function() {
                const cageTypeId = this.value;

                // Clear template select
                templateSelect.innerHTML = '<option value="" selected disabled>Select a template</option>';

                if (cageTypeId) {
                    templateSelect.disabled = false;
                    createTemplateBtn.style.display = 'block';

                    // Fetch templates for selected cage type
                    fetch(`/api/templates/?cage_type_id=${cageTypeId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.length > 0) {
                                data.forEach(template => {
                                    const option = document.createElement('option');
                                    option.value = template.id;
                                    option.textContent = template.name;
                                    templateSelect.appendChild(option);
                                });

                                // Add "Create New Template" option
                                const newOption = document.createElement('option');
                                newOption.value = "new";
                                newOption.textContent = "Create New Template";
                                templateSelect.appendChild(newOption);
                            } else {
                                const option = document.createElement('option');
                                option.value = "new";
                                option.textContent = "Create New Template";
                                templateSelect.appendChild(option);
                            }
                        })
                        .catch(error => console.error('Error fetching templates:', error));
                } else {
                    templateSelect.disabled = true;
                    createTemplateBtn.style.display = 'none';
                }
            });

            // Template select change event
            templateSelect.addEventListener('change', function() {
                if (this.value === "new") {
                    // Show modal or form to create new template
                    const templateName = prompt("Enter a name for the new template:");
                    if (templateName) {
                        const cageTypeId = cageTypeSelect.value;

                        // Create new template
                        fetch('/api/templates/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                name: templateName,
                                cage_type: cageTypeId,
                                description: `Template for ${companySelect.options[companySelect.selectedIndex].text} - ${cageTypeSelect.options[cageTypeSelect.selectedIndex].text}`
                            }),
                        })
                        .then(response => response.json())
                        .then(data => {
                            // Add new template to dropdown and select it
                            const option = document.createElement('option');
                            option.value = data.id;
                            option.textContent = data.name;

                            // Remove the "Create New Template" option
                            const newOption = templateSelect.querySelector('option[value="new"]');
                            if (newOption) {
                                templateSelect.removeChild(newOption);
                            }

                            templateSelect.appendChild(option);
                            templateSelect.appendChild(newOption); // Add it back at the end
                            templateSelect.value = data.id;
                        })
                        .catch(error => console.error('Error creating template:', error));
                    } else {
                        // If user cancels, reset to default
                        templateSelect.value = "";
                    }
                }
            });

            // Create Template button click
            createTemplateBtn.addEventListener('click', function() {
                templateSelect.value = "new";
                const event = new Event('change');
                templateSelect.dispatchEvent(event);
            });

            // File drag and drop
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                dropArea.classList.add('bg-light');
            }

            function unhighlight() {
                dropArea.classList.remove('bg-light');
            }

            dropArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length) {
                    handleFiles(files);
                }
            }

            dropArea.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', () => {
                if (fileInput.files.length) {
                    handleFiles(fileInput.files);
                }
            });

            function handleFiles(files) {
                selectedFile = files[0];

                // Show file name in drop area
                dropArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="bi bi-file-earmark-image"></i>
                    </div>
                    <p>${selectedFile.name}</p>
                    <p class="text-muted small">${formatFileSize(selectedFile.size)}</p>
                    <button type="button" class="btn btn-sm btn-outline-danger mt-2" id="removeFileBtn">
                        <i class="bi bi-x"></i> Remove
                    </button>
                `;

                // Add event listener to remove button
                document.getElementById('removeFileBtn').addEventListener('click', (e) => {
                    e.stopPropagation();
                    resetFileInput();
                });

                // Enable process button if all required fields are filled
                updateProcessButton();
            }

            function resetFileInput() {
                selectedFile = null;
                fileInput.value = '';

                // Reset drop area
                dropArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="bi bi-cloud-arrow-up"></i>
                    </div>
                    <p>Drag & drop an image here or click to browse</p>
                    <p class="text-muted small">Supported formats: JPG, PNG, GIF</p>
                `;

                // Disable process button
                updateProcessButton();
            }

            function formatFileSize(bytes) {
                if (bytes < 1024) return bytes + ' bytes';
                else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
                else return (bytes / 1048576).toFixed(1) + ' MB';
            }

            // Model selection
            modelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    modelBtns.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    selectedModel = btn.getAttribute('data-model');
                });
            });

            // Form submission
            document.getElementById('uploadForm').addEventListener('submit', function(e) {
                e.preventDefault();

                if (!selectedFile) {
                    alert('Please select an image file');
                    return;
                }

                const formData = new FormData();
                formData.append('image', selectedFile);

                // Add template if selected
                if (templateSelect.value && templateSelect.value !== "new") {
                    formData.append('template', templateSelect.value);
                }

                // Disable form elements during upload
                processBtn.disabled = true;
                processBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';

                // Upload the image
                fetch('/api/upload/', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.drawing_id) {
                        // Start analysis with selected model
                        return fetch(`/api/analyze/${data.drawing_id}/`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                ai_model: selectedModel === 'gpt-4.1' ? 'gpt41' :
                                          selectedModel === 'gpt-4o' ? 'gpt4o' : 'claude'
                            })
                        });
                    } else {
                        throw new Error(data.message || 'Upload failed');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Redirect to results page
                    window.location.href = `/static/results.html?id=${data.drawing_id}`;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error: ' + error.message);

                    // Re-enable form elements
                    processBtn.disabled = false;
                    processBtn.innerHTML = '<i class="bi bi-gear"></i> Process Image';
                });
            });

            // Update process button state
            function updateProcessButton() {
                processBtn.disabled = !(selectedFile && companySelect.value && cageTypeSelect.value);
            }

            // Add event listeners to update process button
            companySelect.addEventListener('change', updateProcessButton);
            cageTypeSelect.addEventListener('change', updateProcessButton);
        });
    </script>
</body>
</html>
