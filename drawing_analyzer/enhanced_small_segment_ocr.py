#!/usr/bin/env python3
"""
Enhanced OCR processing for small segments to catch missed text
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import easyocr

def enhance_small_segment_for_ocr(image, min_size=800):
    """
    Enhance small segments for better OCR performance
    
    Args:
        image: PIL Image object
        min_size: Minimum dimension to upscale to
    
    Returns:
        Enhanced PIL Image
    """
    # Convert to numpy array for OpenCV processing
    img_array = np.array(image)
    
    # Get current dimensions
    height, width = img_array.shape[:2]
    current_size = max(height, width)
    
    print(f"📏 Original segment size: {width}x{height}")
    
    # 1. Upscale if too small
    if current_size < min_size:
        scale_factor = min_size / current_size
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        print(f"🔍 Upscaling by {scale_factor:.2f}x to {new_width}x{new_height}")
        
        # Use INTER_CUBIC for better quality upscaling
        img_array = cv2.resize(img_array, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
    
    # 2. Convert back to PIL for further enhancement
    enhanced_image = Image.fromarray(img_array)
    
    # 3. Enhance contrast
    enhancer = ImageEnhance.Contrast(enhanced_image)
    enhanced_image = enhancer.enhance(1.3)  # Increase contrast by 30%
    
    # 4. Enhance sharpness
    enhancer = ImageEnhance.Sharpness(enhanced_image)
    enhanced_image = enhancer.enhance(1.2)  # Increase sharpness by 20%
    
    # 5. Apply slight denoising
    enhanced_image = enhanced_image.filter(ImageFilter.MedianFilter(size=3))
    
    print(f"✅ Enhanced segment ready for OCR")
    return enhanced_image

def process_small_segment_with_enhanced_ocr(image, reader=None, confidence_threshold=0.3):
    """
    Process small segments with enhanced OCR techniques
    
    Args:
        image: PIL Image object
        reader: EasyOCR reader instance (optional)
        confidence_threshold: Minimum confidence for text detection
    
    Returns:
        List of detected text with bounding boxes
    """
    if reader is None:
        reader = easyocr.Reader(['en'])
    
    print(f"🔍 Processing small segment with enhanced OCR")
    
    # Strategy 1: Process original image
    print("📝 Strategy 1: Original image")
    original_results = process_with_easyocr(image, reader, confidence_threshold)
    
    # Strategy 2: Process enhanced image
    print("📝 Strategy 2: Enhanced image")
    enhanced_image = enhance_small_segment_for_ocr(image)
    enhanced_results = process_with_easyocr(enhanced_image, reader, confidence_threshold)
    
    # Strategy 3: Process with different OCR parameters
    print("📝 Strategy 3: Adjusted OCR parameters")
    adjusted_results = process_with_adjusted_params(image, reader)
    
    # Strategy 4: Process grayscale version
    print("📝 Strategy 4: Grayscale conversion")
    grayscale_image = image.convert('L').convert('RGB')
    grayscale_results = process_with_easyocr(grayscale_image, reader, confidence_threshold * 0.8)
    
    # Combine and deduplicate results
    all_results = []
    all_results.extend(original_results)
    all_results.extend(enhanced_results)
    all_results.extend(adjusted_results)
    all_results.extend(grayscale_results)
    
    # Deduplicate based on text content and approximate position
    deduplicated_results = deduplicate_ocr_results(all_results)
    
    print(f"📊 OCR Results Summary:")
    print(f"   Original: {len(original_results)} detections")
    print(f"   Enhanced: {len(enhanced_results)} detections")
    print(f"   Adjusted: {len(adjusted_results)} detections")
    print(f"   Grayscale: {len(grayscale_results)} detections")
    print(f"   Final (deduplicated): {len(deduplicated_results)} detections")
    
    return deduplicated_results

def process_with_easyocr(image, reader, confidence_threshold):
    """Process image with standard EasyOCR"""
    try:
        # Convert PIL to numpy array
        img_array = np.array(image)
        
        # Run EasyOCR
        results = reader.readtext(img_array)
        
        # Convert to our format
        detections = []
        for (bbox, text, confidence) in results:
            if confidence >= confidence_threshold:
                # Calculate bounding box coordinates
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                
                x = int(min(x_coords))
                y = int(min(y_coords))
                width = int(max(x_coords) - min(x_coords))
                height = int(max(y_coords) - min(y_coords))
                
                detections.append({
                    'detected_text': text.strip(),
                    'confidence': confidence,
                    'x': x,
                    'y': y,
                    'width': width,
                    'height': height,
                    'center_x': x + width // 2,
                    'center_y': y + height // 2,
                    'source': 'standard_ocr'
                })
        
        return detections
        
    except Exception as e:
        print(f"❌ EasyOCR processing failed: {str(e)}")
        return []

def process_with_adjusted_params(image, reader):
    """Process with adjusted EasyOCR parameters for small text"""
    try:
        img_array = np.array(image)
        
        # Use more sensitive parameters for small text
        results = reader.readtext(
            img_array,
            width_ths=0.5,      # Lower width threshold
            height_ths=0.5,     # Lower height threshold
            paragraph=False,    # Don't group into paragraphs
            min_size=10,        # Smaller minimum text size
            text_threshold=0.6, # Lower text confidence threshold
            low_text=0.3,       # Lower text detection threshold
            link_threshold=0.3, # Lower link threshold
            canvas_size=2560,   # Larger canvas for better processing
            mag_ratio=1.8       # Higher magnification
        )
        
        # Convert to our format with lower confidence threshold
        detections = []
        for (bbox, text, confidence) in results:
            if confidence >= 0.2:  # Lower threshold for adjusted params
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                
                x = int(min(x_coords))
                y = int(min(y_coords))
                width = int(max(x_coords) - min(x_coords))
                height = int(max(y_coords) - min(y_coords))
                
                detections.append({
                    'detected_text': text.strip(),
                    'confidence': confidence,
                    'x': x,
                    'y': y,
                    'width': width,
                    'height': height,
                    'center_x': x + width // 2,
                    'center_y': y + height // 2,
                    'source': 'adjusted_params'
                })
        
        return detections
        
    except Exception as e:
        print(f"❌ Adjusted OCR processing failed: {str(e)}")
        return []

def deduplicate_ocr_results(results, distance_threshold=30, text_similarity_threshold=0.8):
    """
    Remove duplicate OCR detections based on position and text similarity
    
    Args:
        results: List of OCR detection dictionaries
        distance_threshold: Maximum distance between centers to consider duplicates
        text_similarity_threshold: Minimum text similarity to consider duplicates
    
    Returns:
        List of deduplicated results
    """
    if not results:
        return []
    
    # Sort by confidence (highest first)
    sorted_results = sorted(results, key=lambda x: x['confidence'], reverse=True)
    
    deduplicated = []
    used_indices = set()
    
    for i, result in enumerate(sorted_results):
        if i in used_indices:
            continue
        
        # Keep this result
        deduplicated.append(result)
        used_indices.add(i)
        
        # Mark similar results as used
        for j, other_result in enumerate(sorted_results[i+1:], i+1):
            if j in used_indices:
                continue
            
            # Check if they're duplicates
            if are_duplicate_detections(result, other_result, distance_threshold, text_similarity_threshold):
                used_indices.add(j)
                print(f"🔄 Deduplicated: '{other_result['detected_text']}' (similar to '{result['detected_text']}')")
    
    return deduplicated

def are_duplicate_detections(det1, det2, distance_threshold, text_similarity_threshold):
    """Check if two detections are duplicates"""
    # Check distance between centers
    distance = ((det1['center_x'] - det2['center_x'])**2 + (det1['center_y'] - det2['center_y'])**2)**0.5
    
    if distance > distance_threshold:
        return False
    
    # Check text similarity
    text1 = det1['detected_text'].lower().strip()
    text2 = det2['detected_text'].lower().strip()
    
    # Simple similarity check
    if text1 == text2:
        return True
    
    # Check if one text is contained in the other
    if text1 in text2 or text2 in text1:
        return True
    
    # Check character-level similarity
    similarity = calculate_text_similarity(text1, text2)
    return similarity >= text_similarity_threshold

def calculate_text_similarity(text1, text2):
    """Calculate simple text similarity"""
    if not text1 or not text2:
        return 0.0
    
    # Simple character overlap ratio
    set1 = set(text1)
    set2 = set(text2)
    
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    
    return intersection / union if union > 0 else 0.0

def test_enhanced_ocr():
    """Test the enhanced OCR on a sample image"""
    print("🧪 Testing Enhanced Small Segment OCR")
    print("=" * 50)
    
    # This would be called from the main processing pipeline
    # when a segment is detected as small
    print("This enhanced OCR system will:")
    print("✅ Upscale small segments for better text recognition")
    print("✅ Apply contrast and sharpness enhancement")
    print("✅ Use multiple OCR strategies")
    print("✅ Adjust EasyOCR parameters for small text")
    print("✅ Process grayscale versions")
    print("✅ Deduplicate results intelligently")
    print("✅ Catch text that standard OCR misses")

if __name__ == "__main__":
    test_enhanced_ocr()
