#!/usr/bin/env python3
"""
Test actual GPT Vision functionality with real image processing
"""

import requests
import base64
from PIL import Image, ImageDraw, ImageFont
import io
import json

def create_clear_test_image():
    """Create a clear test image with engineering symbols"""
    # Create a larger, clearer image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a larger font
    try:
        # Use default font but larger
        font_size = 24
    except:
        font_size = 20
    
    # Draw clear engineering symbols
    draw.text((50, 80), "Ø25.4", fill='black', font=None)
    draw.text((150, 80), "±0.05", fill='black', font=None)
    draw.text((250, 80), "R0.15", fill='black', font=None)
    
    return img

def test_gpt_vision_direct():
    """Test GPT Vision directly with a clear image"""
    print("🔍 Testing GPT Vision Direct API Call")
    print("=" * 50)
    
    # Create test image
    test_image = create_clear_test_image()
    
    # Convert to base64
    buffered = io.BytesIO()
    test_image.save(buffered, format="PNG")
    img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
    
    # Test with the working endpoint
    endpoint = 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview'
    api_key = '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACYeBjFXJ3w3AAAAACOGpNZC'
    
    headers = {
        'Content-Type': 'application/json',
        'api-key': api_key
    }
    
    # Simple vision test
    payload = {
        "model": "gpt-4o",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "What engineering symbols do you see in this image? List them clearly."
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{img_base64}"
                        }
                    }
                ]
            }
        ],
        "max_tokens": 100,
        "temperature": 0.1
    }
    
    try:
        print("📤 Sending vision request...")
        response = requests.post(endpoint, headers=headers, json=payload, timeout=30)
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ GPT Vision Response: {content}")
            
            # Test with enhanced spatial analysis prompt
            print("\n🎯 Testing Enhanced Spatial Analysis...")
            
            enhanced_payload = {
                "model": "gpt-4o",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """Analyze this engineering drawing image and return ONLY valid JSON:
{
    "values": ["symbol1", "symbol2"],
    "symbol_types": ["type1", "type2"],
    "spatial_analysis": {
        "symbols_on_same_line": true,
        "horizontal_alignment": "well_aligned",
        "recommended_action": "merge_all",
        "reasoning": "explanation"
    }
}"""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{img_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 200,
                "temperature": 0.1
            }
            
            enhanced_response = requests.post(endpoint, headers=headers, json=enhanced_payload, timeout=30)
            
            if enhanced_response.status_code == 200:
                enhanced_result = enhanced_response.json()
                enhanced_content = enhanced_result['choices'][0]['message']['content']
                print(f"✅ Enhanced Response: {enhanced_content}")
                
                # Try to parse as JSON
                try:
                    # Clean the response (remove markdown if present)
                    clean_content = enhanced_content.strip()
                    if clean_content.startswith('```json'):
                        clean_content = clean_content.replace('```json', '').replace('```', '').strip()
                    
                    parsed_json = json.loads(clean_content)
                    print(f"✅ JSON Parsing: SUCCESS")
                    print(f"   Values: {parsed_json.get('values', [])}")
                    print(f"   Types: {parsed_json.get('symbol_types', [])}")
                    
                    spatial = parsed_json.get('spatial_analysis', {})
                    if spatial:
                        print(f"   Same Line: {spatial.get('symbols_on_same_line', 'N/A')}")
                        print(f"   Recommendation: {spatial.get('recommended_action', 'N/A')}")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON Parsing Failed: {e}")
                    print(f"   Raw content: {enhanced_content}")
                
            else:
                print(f"❌ Enhanced request failed: {enhanced_response.status_code}")
                print(f"   Error: {enhanced_response.text}")
            
        else:
            print(f"❌ Vision request failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_process_b_function():
    """Test the actual Process B GPT Vision function"""
    print("\n🔧 Testing Process B GPT Vision Function")
    print("=" * 50)
    
    try:
        # Import the function
        import os
        import sys
        import django
        
        # Setup Django
        sys.path.append('/home/<USER>/manu/drawing_analyzer')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
        django.setup()
        
        from api.utils.process_b_gpt_vision import process_bounding_box_with_gpt_vision
        
        # Create test image
        test_image = create_clear_test_image()
        
        print("📤 Calling process_bounding_box_with_gpt_vision...")
        result = process_bounding_box_with_gpt_vision(
            cropped_image=test_image,
            detected_text="Ø25.4 ±0.05 R0.15"
        )
        
        print(f"📥 Function Result:")
        print(f"   Values: {result.get('values', [])}")
        print(f"   Types: {result.get('symbol_types', [])}")
        print(f"   Count: {result.get('count', 0)}")
        
        spatial = result.get('spatial_analysis', {})
        if spatial:
            print(f"   Spatial Analysis:")
            print(f"     Same Line: {spatial.get('symbols_on_same_line', 'N/A')}")
            print(f"     Recommendation: {spatial.get('recommended_action', 'N/A')}")
            print(f"     Reasoning: {spatial.get('reasoning', 'N/A')}")
        else:
            print(f"   ❌ No spatial analysis returned")
        
        if result.get('values') and len(result.get('values', [])) > 0:
            print("✅ Process B function is working!")
        else:
            print("❌ Process B function is not working properly")
            print(f"   Raw response: {result.get('raw_response', 'No raw response')}")
        
    except Exception as e:
        print(f"❌ Process B function test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    print("🧪 COMPREHENSIVE GPT VISION TEST")
    print("=" * 60)
    
    # Test 1: Direct API call
    test_gpt_vision_direct()
    
    # Test 2: Process B function
    test_process_b_function()
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
