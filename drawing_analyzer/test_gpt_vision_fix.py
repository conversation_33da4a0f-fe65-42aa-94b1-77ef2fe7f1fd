#!/usr/bin/env python3
"""
Test the fixed GPT Vision implementation
"""

import os
import sys
import django

# Setup Django
sys.path.append('/home/<USER>/manu/drawing_analyzer')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from PIL import Image, ImageDraw
import io
from api.utils.process_b_gpt_vision import process_bounding_box_with_gpt_vision

def create_test_image():
    """Create a test image with engineering symbols"""
    # Create a simple image with engineering text
    img = Image.new('RGB', (300, 150), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw some engineering symbols
    draw.text((20, 40), "Ø25.4", fill='black')
    draw.text((100, 40), "±0.05", fill='black')
    draw.text((20, 80), "R0.15", fill='black')
    
    return img

def test_gpt_vision():
    """Test the GPT Vision functionality"""
    print("🧪 Testing GPT Vision with Enhanced Spatial Analysis")
    print("=" * 60)
    
    # Create test image
    test_image = create_test_image()
    
    # Test case 1: Multiple symbols on same line
    print("\n📝 Test Case 1: Multiple symbols on same line")
    print("   Symbols: Ø25.4 ±0.05")
    
    try:
        result = process_bounding_box_with_gpt_vision(
            cropped_image=test_image,
            detected_text="Ø25.4 ±0.05"
        )
        
        print(f"   ✅ GPT Vision Response:")
        print(f"      Values: {result.get('values', [])}")
        print(f"      Types: {result.get('symbol_types', [])}")
        print(f"      Count: {result.get('count', 0)}")
        
        # Check spatial analysis
        spatial = result.get('spatial_analysis', {})
        if spatial:
            print(f"   🔍 Spatial Analysis:")
            print(f"      Same Line: {spatial.get('symbols_on_same_line', 'N/A')}")
            print(f"      Horizontal Alignment: {spatial.get('horizontal_alignment', 'N/A')}")
            print(f"      Symbol Grouping: {spatial.get('symbol_grouping', 'N/A')}")
            print(f"      Recommended Action: {spatial.get('recommended_action', 'N/A')}")
            print(f"      Reasoning: {spatial.get('reasoning', 'N/A')}")
        else:
            print("   ⚠️ No spatial analysis returned")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Test case 2: Single symbol
    print("\n📝 Test Case 2: Single symbol")
    print("   Symbol: R0.15")
    
    try:
        single_img = Image.new('RGB', (100, 50), color='white')
        draw = ImageDraw.Draw(single_img)
        draw.text((10, 15), "R0.15", fill='black')
        
        result = process_bounding_box_with_gpt_vision(
            cropped_image=single_img,
            detected_text="R0.15"
        )
        
        print(f"   ✅ GPT Vision Response:")
        print(f"      Values: {result.get('values', [])}")
        print(f"      Types: {result.get('symbol_types', [])}")
        
        # Check spatial analysis
        spatial = result.get('spatial_analysis', {})
        if spatial:
            print(f"   🔍 Spatial Analysis:")
            print(f"      Recommended Action: {spatial.get('recommended_action', 'N/A')}")
            print(f"      Reasoning: {spatial.get('reasoning', 'N/A')}")
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✅ GPT Vision Test Complete")
    print("=" * 60)

if __name__ == "__main__":
    test_gpt_vision()
