#!/usr/bin/env python3
"""
Test all 3 working endpoints for optimal load balancing
"""

import requests
import time

# Test all 3 endpoints
endpoints = [
    {
        'name': 'GPT-4o-Secondary',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACYeBjFXJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    {
        'name': 'GPT-4.1-Secondary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BCACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1'
    },
    {
        'name': 'GPT-4.1-2-Tertiary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1-2/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BCACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1-2'
    }
]

def test_endpoint(endpoint_config, test_num):
    """Test a single endpoint"""
    print(f"\n🧪 Test {test_num}: {endpoint_config['name']}")
    
    try:
        start_time = time.time()
        
        headers = {
            'Content-Type': 'application/json',
            'api-key': endpoint_config['api_key']
        }
        
        payload = {
            "model": endpoint_config['model'],
            "messages": [
                {
                    "role": "user",
                    "content": f"Respond with: {endpoint_config['name']} working"
                }
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        response = requests.post(
            endpoint_config['endpoint'],
            headers=headers,
            json=payload,
            timeout=15
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            content = response.json()['choices'][0]['message']['content']
            print(f"   ✅ Success: {content}")
            print(f"   ⏱️ Response time: {response_time:.2f}s")
            return True, response_time
        else:
            print(f"   ❌ Failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False, response_time
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False, 0

def main():
    print("🚀 Testing TRIPLE Endpoint Load Balancing")
    print("=" * 55)
    
    working_endpoints = []
    total_tests = 9  # 3 rounds of 3 endpoints each
    
    # Test cycling through all 3 endpoints (simulating load balancing)
    for i in range(total_tests):
        endpoint_index = i % len(endpoints)
        endpoint = endpoints[endpoint_index]
        
        success, response_time = test_endpoint(endpoint, i + 1)
        
        if success:
            working_endpoints.append({
                'name': endpoint['name'],
                'response_time': response_time
            })
        
        # Small delay between requests
        time.sleep(0.3)
    
    print("\n" + "=" * 55)
    print("📊 TRIPLE LOAD BALANCING RESULTS")
    print("=" * 55)
    
    if len(working_endpoints) > 0:
        print(f"✅ Successful requests: {len(working_endpoints)}/{total_tests}")
        
        # Group by endpoint type
        gpt4o_times = [e['response_time'] for e in working_endpoints if 'GPT-4o' in e['name']]
        gpt41_times = [e['response_time'] for e in working_endpoints if 'GPT-4.1-Secondary' in e['name']]
        gpt412_times = [e['response_time'] for e in working_endpoints if 'GPT-4.1-2' in e['name']]
        
        print("\n📈 Performance Analysis:")
        if gpt4o_times:
            avg_gpt4o = sum(gpt4o_times) / len(gpt4o_times)
            print(f"   GPT-4o:   {avg_gpt4o:.2f}s avg ({len(gpt4o_times)} requests)")
        
        if gpt41_times:
            avg_gpt41 = sum(gpt41_times) / len(gpt41_times)
            print(f"   GPT-4.1:  {avg_gpt41:.2f}s avg ({len(gpt41_times)} requests)")
        
        if gpt412_times:
            avg_gpt412 = sum(gpt412_times) / len(gpt412_times)
            print(f"   GPT-4.1-2: {avg_gpt412:.2f}s avg ({len(gpt412_times)} requests)")
        
        if len(working_endpoints) == total_tests:
            print("\n🎉 PERFECT! All 3 endpoints working flawlessly")
            print("✅ Triple load balancing provides:")
            print("   - Maximum speed and throughput")
            print("   - Excellent redundancy (2 backup endpoints)")
            print("   - Optimal resource distribution")
            print("   - High availability and reliability")
            
            # Calculate overall performance
            avg_overall = sum(e['response_time'] for e in working_endpoints) / len(working_endpoints)
            print(f"\n📊 Overall average response time: {avg_overall:.2f}s")
            
        else:
            success_rate = (len(working_endpoints) / total_tests) * 100
            print(f"\n⚠️ Success rate: {success_rate:.1f}% ({len(working_endpoints)}/{total_tests})")
            print("Some endpoints may be intermittent")
    else:
        print("❌ No successful requests - all endpoints failing")
    
    return len(working_endpoints) == total_tests

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 RECOMMENDATION: TRIPLE ENDPOINT CONFIGURATION OPTIMAL!")
        print("The Process B system now has MAXIMUM performance:")
        print("  ✅ 3 working endpoints (GPT-4o, GPT-4.1, GPT-4.1-2)")
        print("  ✅ Triple load balancing")
        print("  ✅ Maximum redundancy")
        print("  ✅ Fastest possible processing")
        print("  ✅ Enterprise-grade reliability")
    else:
        print(f"\n⚠️ RECOMMENDATION: Monitor endpoint stability")
        print("Some endpoints may need attention")
