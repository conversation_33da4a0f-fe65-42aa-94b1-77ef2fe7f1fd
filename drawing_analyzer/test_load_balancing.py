#!/usr/bin/env python3
"""
Test the load balancing with both working endpoints
"""

import os
import sys
import django

# Setup Django
sys.path.append('/home/<USER>/manu/drawing_analyzer')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from PIL import Image, ImageDraw
from api.utils.process_b_gpt_vision import process_bounding_box_with_gpt_vision

def create_test_image():
    """Create a test image with engineering symbols"""
    img = Image.new('RGB', (300, 100), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((20, 30), "Ø25.4 ±0.05", fill='black')
    return img

def test_load_balancing():
    """Test load balancing between the two working endpoints"""
    print("🔄 Testing Load Balancing with 2 Working Endpoints")
    print("=" * 60)
    
    test_image = create_test_image()
    
    # Test multiple requests to see load balancing in action
    for i in range(4):
        print(f"\n📝 Request {i+1}:")
        try:
            result = process_bounding_box_with_gpt_vision(
                cropped_image=test_image,
                detected_text="Ø25.4 ±0.05"
            )
            
            if result and 'values' in result:
                print(f"   ✅ Success: {result['values']}")
                spatial = result.get('spatial_analysis', {})
                if spatial:
                    print(f"   🎯 Recommendation: {spatial.get('recommended_action', 'N/A')}")
            else:
                print(f"   ❌ Failed or unexpected response")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✅ Load Balancing Test Complete")
    print("=" * 60)
    print("If you see different endpoint numbers (1/2, 2/2), load balancing is working!")

if __name__ == "__main__":
    test_load_balancing()
