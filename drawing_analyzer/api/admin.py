from django.contrib import admin
from django.utils.safestring import mark_safe
from .models import (
    Company,
    CageType,
    Template,
    Drawing,
    Segment,
    Symbol,
    SegmentSimilarity,
    AnalysisLog,
    EasyOCRDetection,
    BoundingBox
)

# Inline admin classes
class CageTypeInline(admin.TabularInline):
    model = CageType
    extra = 1

class TemplateInline(admin.TabularInline):
    model = Template
    extra = 1

class SegmentInline(admin.TabularInline):
    model = Segment
    extra = 0
    fields = ('segment_number', 'image', 'marked_image')
    readonly_fields = ('image', 'marked_image')
    show_change_link = True
    max_num = 10  # Limit the number of segments shown

class SymbolInline(admin.TabularInline):
    model = Symbol
    extra = 0
    fields = ('symbol_type', 'value', 'x_coordinate', 'y_coordinate', 'is_marked')
    show_change_link = True
    max_num = 10  # Limit the number of symbols shown

class AnalysisLogInline(admin.TabularInline):
    model = AnalysisLog
    extra = 0
    fields = ('timestamp', 'message')
    readonly_fields = ('timestamp', 'message')
    max_num = 5  # Limit the number of logs shown

# Company admin with inline CageTypes
@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description')
    search_fields = ('name', 'description')
    inlines = [CageTypeInline]

# CageType admin with inline Templates
@admin.register(CageType)
class CageTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'company', 'description')
    list_filter = ('company',)
    search_fields = ('name', 'description')
    inlines = [TemplateInline]

# Template admin
@admin.register(Template)
class TemplateAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'cage_type', 'description')
    list_filter = ('cage_type', 'cage_type__company')
    search_fields = ('name', 'description')

# Custom admin for Drawing
@admin.register(Drawing)
class DrawingAdmin(admin.ModelAdmin):
    list_display = ('id', 'original_filename', 'uploaded_at', 'processed', 'finalized', 'is_template', 'template')
    list_filter = ('processed', 'finalized', 'is_template', 'uploaded_at', 'template')
    search_fields = ('original_filename',)
    date_hierarchy = 'uploaded_at'
    readonly_fields = ('image_preview', 'final_image_preview')
    fieldsets = (
        (None, {
            'fields': ('original_filename', 'image', 'image_preview')
        }),
        ('Status', {
            'fields': ('processed', 'finalized', 'is_template')
        }),
        ('Template Information', {
            'fields': ('template',)
        }),
        ('Output Files', {
            'fields': ('final_image', 'final_image_preview', 'finalized_image', 'measurements_excel', 'summary_file')
        }),
    )
    inlines = [SegmentInline, AnalysisLogInline]

    def image_preview(self, obj):
        """Display a preview of the image"""
        if obj.image:
            return mark_safe(f'<img src="{obj.image.url}" width="300" />')
        return "No image"

    def final_image_preview(self, obj):
        """Display a preview of the final image"""
        if obj.final_image:
            return mark_safe(f'<img src="{obj.final_image.url}" width="300" />')
        return "No final image"

    image_preview.short_description = 'Image Preview'
    final_image_preview.short_description = 'Final Image Preview'

# Custom admin for Segment
@admin.register(Segment)
class SegmentAdmin(admin.ModelAdmin):
    list_display = ('id', 'drawing', 'segment_number', 'segment_x_offset', 'segment_y_offset')
    list_filter = ('drawing',)
    search_fields = ('drawing__original_filename', 'segment_number')
    readonly_fields = ('image_preview', 'marked_image_preview')
    fieldsets = (
        (None, {
            'fields': ('drawing', 'segment_number')
        }),
        ('Position', {
            'fields': ('segment_x_offset', 'segment_y_offset')
        }),
        ('Images', {
            'fields': ('image', 'image_preview', 'marked_image', 'marked_image_preview', 'grid_image', 'annotated_image')
        }),
    )
    inlines = [SymbolInline]

    def image_preview(self, obj):
        """Display a preview of the segment image"""
        if obj.image:
            return mark_safe(f'<img src="{obj.image.url}" width="300" />')
        return "No image"

    def marked_image_preview(self, obj):
        """Display a preview of the marked image"""
        if obj.marked_image:
            return mark_safe(f'<img src="{obj.marked_image.url}" width="300" />')
        return "No marked image"

    image_preview.short_description = 'Image Preview'
    marked_image_preview.short_description = 'Marked Image Preview'

# Custom admin for Symbol
@admin.register(Symbol)
class SymbolAdmin(admin.ModelAdmin):
    list_display = ('id', 'segment', 'symbol_type', 'value', 'x_coordinate', 'y_coordinate', 'normalized_x', 'normalized_y', 'is_marked', 'position_source')
    list_filter = ('symbol_type', 'is_marked', 'position_source', 'segment__drawing')
    search_fields = ('value', 'description')
    readonly_fields = ('normalized_x', 'normalized_y')

# Custom admin for SegmentSimilarity
@admin.register(SegmentSimilarity)
class SegmentSimilarityAdmin(admin.ModelAdmin):
    list_display = ('id', 'segment', 'similar_segment', 'similarity_score', 'user_confirmed', 'created_at')
    list_filter = ('user_confirmed', 'created_at')
    search_fields = ('segment__drawing__original_filename', 'similar_segment__drawing__original_filename')

# Custom admin for AnalysisLog
@admin.register(AnalysisLog)
class AnalysisLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'drawing', 'timestamp', 'message_preview')
    list_filter = ('drawing', 'timestamp')
    search_fields = ('message', 'drawing__original_filename')

    def message_preview(self, obj):
        """Return a preview of the message"""
        if len(obj.message) > 50:
            return f"{obj.message[:50]}..."
        return obj.message

    message_preview.short_description = 'Message'

# Custom admin for EasyOCRDetection
@admin.register(EasyOCRDetection)
class EasyOCRDetectionAdmin(admin.ModelAdmin):
    list_display = ('id', 'segment', 'detected_text', 'confidence', 'center_x', 'center_y', 'normalized_x', 'normalized_y', 'orientation', 'is_matched', 'matched_symbol', 'similarity_score', 'created_at')
    list_filter = ('is_matched', 'orientation', 'segment__drawing', 'created_at')
    search_fields = ('detected_text', 'matched_symbol__value', 'segment__drawing__original_filename')
    readonly_fields = ('created_at', 'normalized_x', 'normalized_y')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('segment', 'matched_symbol')

# Custom admin for BoundingBox (Process B)
@admin.register(BoundingBox)
class BoundingBoxAdmin(admin.ModelAdmin):
    list_display = ('id', 'segment', 'detected_text', 'confidence', 'x', 'y', 'width', 'height', 'center_x', 'center_y', 'processed', 'confirmed_symbol', 'created_at')
    list_filter = ('processed', 'segment__drawing', 'segment__drawing__process_type', 'created_at')
    search_fields = ('detected_text', 'segment__drawing__original_filename')
    readonly_fields = ('created_at', 'center_x', 'center_y')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('segment', 'confirmed_symbol')
