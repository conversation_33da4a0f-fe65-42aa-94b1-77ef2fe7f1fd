import os
import logging
from django.shortcuts import get_object_or_404
from django.conf import settings
from django.http import Http404
# from django.views.decorators.csrf import csrf_exempt  # No longer needed - CSRF disabled
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, action
from rest_framework.response import Response
from rest_framework.views import APIView
import cv2
import numpy as np


def enhance_image_for_engineering_ocr(image_array):
    """
    Apply optimized image enhancement techniques for engineering drawings OCR
    to improve detection of both small symbols and longer text values

    Args:
        image_array: numpy array of the image

    Returns:
        Enhanced numpy array optimized for OCR detection
    """
    # Convert to grayscale if needed
    if len(image_array.shape) == 3:
        gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = image_array.copy()

    # Multi-stage enhancement for better text detection

    # 1. Adaptive histogram equalization for better contrast
    clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8,8))
    clahe_enhanced = clahe.apply(gray)

    # 2. Morphological operations to clean up text and connect broken characters
    # This helps with longer text values that might be fragmented
    kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 1))  # Horizontal connection
    morphed = cv2.morphologyEx(clahe_enhanced, cv2.MORPH_CLOSE, kernel_close)

    # 3. Gentle sharpening to improve edge definition
    sharpen_kernel = np.array([[0,-1,0], [-1,5,-1], [0,-1,0]])
    sharpened = cv2.filter2D(morphed, -1, sharpen_kernel)

    # 4. Ensure values are in valid range
    sharpened = np.clip(sharpened, 0, 255)

    # 5. Final noise reduction while preserving text edges
    denoised = cv2.bilateralFilter(sharpened, 3, 30, 30)  # Reduced parameters to preserve detail

    # Convert back to RGB format for EasyOCR
    enhanced_rgb = cv2.cvtColor(denoised, cv2.COLOR_GRAY2RGB)

    return enhanced_rgb

# Test View
@api_view(['GET'])
def test_view(request):
    """
    A simple test view to check if the API is working
    """
    print("Test view called")

    # Check if media directory exists
    import os
    media_root = settings.MEDIA_ROOT
    media_exists = os.path.exists(media_root)
    media_writable = os.access(media_root, os.W_OK)

    # Check if drawings directory exists
    drawings_dir = os.path.join(media_root, 'drawings')
    drawings_exists = os.path.exists(drawings_dir)
    drawings_writable = os.access(drawings_dir, os.W_OK) if drawings_exists else False

    # Check if segments directory exists
    segments_dir = os.path.join(media_root, 'segments')
    segments_exists = os.path.exists(segments_dir)
    segments_writable = os.access(segments_dir, os.W_OK) if segments_exists else False

    return Response({
        'status': 'ok',
        'message': 'API is working',
        'media_root': media_root,
        'media_exists': media_exists,
        'media_writable': media_writable,
        'drawings_dir': drawings_dir,
        'drawings_exists': drawings_exists,
        'drawings_writable': drawings_writable,
        'segments_dir': segments_dir,
        'segments_exists': segments_exists,
        'segments_writable': segments_writable
    })

from .models import Drawing, Segment, Symbol, AnalysisLog, Company, CageType, Template, SegmentSimilarity, BoundingBox
from .serializers import (
    DrawingSerializer, DrawingDetailSerializer, SegmentSerializer, SegmentDetailSerializer,
    SymbolSerializer, AnalysisLogSerializer, CompanySerializer, CageTypeSerializer,
    TemplateSerializer, SegmentSimilaritySerializer, UploadDrawingSerializer,
    AnalyzeDrawingSerializer, MarkSymbolSerializer, UpdateSymbolPositionSerializer,
    EditSymbolSerializer, AddSymbolSerializer, BulkDeleteSymbolsSerializer,
    AdjustBubbleSizeSerializer, ConfirmSimilaritySerializer, AddSymbolAtClickSerializer,
    BoundingBoxSerializer, ProcessBSymbolConfirmationSerializer
)

# Upload Drawing
@api_view(['POST'])
def upload_drawing(request):
    try:
        print("=" * 80)
        print("UPLOAD DRAWING REQUEST RECEIVED")
        print("=" * 80)
        print(f"Request data keys: {request.data.keys()}")
        print(f"Request FILES: {request.FILES}")
        print(f"Content-Type: {request.content_type}")

        # Check media directories
        media_root = settings.MEDIA_ROOT
        print(f"Media root: {media_root}")
        print(f"Media root exists: {os.path.exists(media_root)}")
        print(f"Media root is writable: {os.access(media_root, os.W_OK)}")

        drawings_dir = os.path.join(media_root, 'drawings')
        print(f"Drawings dir: {drawings_dir}")
        print(f"Drawings dir exists: {os.path.exists(drawings_dir)}")
        print(f"Drawings dir is writable: {os.access(drawings_dir, os.W_OK) if os.path.exists(drawings_dir) else False}")

        # Create directories if they don't exist
        os.makedirs(media_root, exist_ok=True)
        os.makedirs(drawings_dir, exist_ok=True)

        # Check if 'image' is in the request
        if 'image' not in request.FILES:
            print("No image file found in request")
            return Response({
                'error': 'No image file found in request',
                'message': 'Please upload an image file'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get the image file
        image_file = request.FILES['image']
        print(f"Image file: {image_file.name}, size: {image_file.size} bytes, type: {image_file.content_type}")

        # Check if the file is an image
        if not image_file.content_type.startswith('image/'):
            print(f"Invalid file type: {image_file.content_type}")
            return Response({
                'error': f'Invalid file type: {image_file.content_type}',
                'message': 'Please upload an image file'
            }, status=status.HTTP_400_BAD_REQUEST)

        print("Creating serializer...")
        serializer = UploadDrawingSerializer(data=request.data)
        print(f"Checking if serializer is valid...")
        if serializer.is_valid():
            print("Serializer is valid")
            print(f"Validated data: {serializer.validated_data}")

            try:
                # Create drawing object
                print("Creating drawing object...")
                drawing = Drawing.objects.create(
                    image=serializer.validated_data['image'],
                    original_filename=serializer.validated_data['image'].name,
                    template=serializer.validated_data.get('template')
                )

                # Log company and cage type information if provided
                company = serializer.validated_data.get('company')
                cage_type = serializer.validated_data.get('cage_type')

                if company:
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message=f"Company: {company.name}"
                    )

                if cage_type:
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message=f"Cage Type: {cage_type.name}"
                    )

                print(f"Drawing created with ID: {drawing.id}")
                print(f"Image path: {drawing.image.path}")
                print(f"Image URL: {drawing.image.url}")

                # Create media directories
                segments_dir = os.path.join(settings.MEDIA_ROOT, 'segments', f'drawing_{drawing.id}')
                os.makedirs(segments_dir, exist_ok=True)
                print(f"Created segments directory: {segments_dir}")

                # Log progress
                print("Creating analysis log...")
                AnalysisLog.objects.create(
                    drawing=drawing,
                    message=f"Drawing uploaded: {drawing.original_filename}"
                )

                print("Returning success response")
                return Response({
                    'drawing_id': drawing.id,
                    'message': 'Drawing uploaded successfully'
                }, status=status.HTTP_201_CREATED)
            except Exception as create_error:
                import traceback
                print("=" * 80)
                print(f"ERROR CREATING DRAWING: {str(create_error)}")
                print("=" * 80)
                print(traceback.format_exc())
                return Response({
                    'error': str(create_error),
                    'message': 'Error creating drawing'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            print("=" * 80)
            print(f"SERIALIZER ERRORS: {serializer.errors}")
            print("=" * 80)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        import traceback
        print("=" * 80)
        print(f"ERROR IN UPLOAD_DRAWING: {str(e)}")
        print("=" * 80)
        print(traceback.format_exc())
        return Response({
            'error': str(e),
            'message': 'An error occurred during upload'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Analyze Drawing
@api_view(['POST'])
def analyze_drawing(request, drawing_id):
    try:
        serializer = AnalyzeDrawingSerializer(data=request.data)
        if serializer.is_valid():
            # Get drawing
            drawing = get_object_or_404(Drawing, id=drawing_id)

            # Import utility functions
            from api.utils import encode_image_to_base64, call_gpt41_api, call_llama4_api, parse_gpt41_response, segment_image
            import traceback

            # Log the analysis start
            ai_model = serializer.validated_data.get('ai_model', 'gpt41')
            AnalysisLog.objects.create(
                drawing=drawing,
                message=f"Analysis started with {ai_model} model"
            )

            try:
                # Get the image path
                image_path = drawing.image.path

                # Log segmentation start
                AnalysisLog.objects.create(
                    drawing=drawing,
                    message=f"Segmenting image into 4 parts"
                )

                # Segment the image
                segments_info = segment_image(image_path, num_segments=4)

                if not segments_info:
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message="Error segmenting image - no segments created"
                    )
                    return Response({
                        'message': 'Error segmenting image',
                        'drawing_id': drawing_id
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                # Process each segment with GPT-4.1
                for segment_info in segments_info:
                    segment_number = segment_info['number']
                    segment_path = segment_info['path']

                    # Log segment processing
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message=f"Processing segment {segment_number} with GPT-4.1"
                    )

                    try:
                        # Create segment in database
                        from django.core.files.base import ContentFile
                        import os
                        from django.core.files import File

                        # Create a file object from the segment path
                        with open(segment_path, 'rb') as f:
                            segment_file = File(f)
                            segment_filename = f"segment_{segment_number}.png"

                            # Create segment in database with the segment image
                            segment = Segment.objects.create(
                                drawing=drawing,
                                segment_number=segment_number,
                                segment_x_offset=segment_info['x_offset'],
                                segment_y_offset=segment_info['y_offset']
                            )

                            # Save the segment image
                            segment.image.save(segment_filename, segment_file, save=True)

                        # Encode segment image to base64
                        segment_base64 = encode_image_to_base64(segment_path)

                        if segment_base64:
                            # Process both normal and rotated orientations
                            all_symbols_data = []

                            # Log that we're processing both orientations
                            AnalysisLog.objects.create(
                                drawing=drawing,
                                message=f"Processing segment {segment_number} in both normal and 90° rotated orientations"
                            )

                            # Create a rotated version of the segment
                            rotated_segment_path = None
                            try:
                                from PIL import Image
                                import os

                                # Open the segment image
                                segment_img = Image.open(segment_path)

                                # Rotate 90 degrees clockwise
                                rotated_img = segment_img.rotate(-90, expand=True)  # -90 for clockwise rotation

                                # Save the rotated image
                                rotated_segment_path = os.path.join(os.path.dirname(segment_path), f"rotated_segment_{segment_number}.png")
                                rotated_img.save(rotated_segment_path)

                                # Encode rotated image to base64
                                rotated_segment_base64 = encode_image_to_base64(rotated_segment_path)
                            except Exception as rotation_error:
                                print(f"Error creating rotated segment: {str(rotation_error)}")
                                # Log the error
                                AnalysisLog.objects.create(
                                    drawing=drawing,
                                    message=f"Error creating rotated segment: {str(rotation_error)}"
                                )
                                rotated_segment_base64 = None

                            # Process normal orientation (batch 1 of 2)
                            if ai_model == 'llama4':
                                response_text, orientation = call_llama4_api(
                                    segment_base64,
                                    segment_number,
                                    orientation='normal',
                                    batch_id=0,
                                    batch_size=2,
                                    batch_delay=1.0
                                )
                            else:
                                # Default to GPT-4.1 for other models (claude, gpt4o, gpt41)
                                response_text, orientation = call_gpt41_api(
                                    segment_base64,
                                    segment_number,
                                    orientation='normal',
                                    batch_id=0,
                                    batch_size=2,
                                    batch_delay=1.0
                                )

                            # Parse the response for normal orientation
                            normal_symbols_data = parse_gpt41_response(response_text, orientation='normal')
                            all_symbols_data.extend(normal_symbols_data)

                            # Log normal orientation results
                            AnalysisLog.objects.create(
                                drawing=drawing,
                                message=f"Detected {len(normal_symbols_data)} symbols in segment {segment_number} (normal orientation)"
                            )

                            # Process rotated orientation if available (batch 2 of 2)
                            if rotated_segment_base64:
                                if ai_model == 'llama4':
                                    response_text, orientation = call_llama4_api(
                                        rotated_segment_base64,
                                        segment_number,
                                        orientation='rotated',
                                        batch_id=1,
                                        batch_size=2,
                                        batch_delay=1.0
                                    )
                                else:
                                    # Default to GPT-4.1 for other models (claude, gpt4o, gpt41)
                                    response_text, orientation = call_gpt41_api(
                                        rotated_segment_base64,
                                        segment_number,
                                        orientation='rotated',
                                        batch_id=1,
                                        batch_size=2,
                                        batch_delay=1.0
                                    )

                                # Parse the response for rotated orientation
                                rotated_symbols_data = parse_gpt41_response(response_text, orientation='rotated')
                                all_symbols_data.extend(rotated_symbols_data)

                                # Log rotated orientation results
                                AnalysisLog.objects.create(
                                    drawing=drawing,
                                    message=f"Detected {len(rotated_symbols_data)} symbols in segment {segment_number} (rotated orientation)"
                                )

                                # Clean up rotated segment file
                                try:
                                    if os.path.exists(rotated_segment_path):
                                        os.remove(rotated_segment_path)
                                except Exception as cleanup_error:
                                    print(f"Error cleaning up rotated segment file: {cleanup_error}")
                                    # Log the error
                                    AnalysisLog.objects.create(
                                        drawing=drawing,
                                        message=f"Error cleaning up rotated segment file: {cleanup_error}"
                                    )

                            if all_symbols_data:
                                # Create symbols in database
                                for symbol_data in all_symbols_data:
                                    Symbol.objects.create(
                                        segment=segment,
                                        symbol_type=symbol_data['type'],
                                        value=symbol_data['value'],
                                        x_coordinate=symbol_data['x'],
                                        y_coordinate=symbol_data['y'],
                                        is_marked=symbol_data['marked'],
                                        orientation=symbol_data['orientation']
                                    )

                                # Log total symbols detected
                                marked_count = sum(1 for s in all_symbols_data if s['marked'])
                                normal_count = sum(1 for s in all_symbols_data if s['orientation'] == 'normal')
                                rotated_count = sum(1 for s in all_symbols_data if s['orientation'] == 'rotated')

                                AnalysisLog.objects.create(
                                    drawing=drawing,
                                    message=f"Total: {len(all_symbols_data)} symbols in segment {segment_number}, "
                                            f"{normal_count} in normal orientation, {rotated_count} in rotated orientation, "
                                            f"{marked_count} marked as significant"
                                )
                            else:
                                # Log that no symbols were found
                                AnalysisLog.objects.create(
                                    drawing=drawing,
                                    message=f"No symbols detected in segment {segment_number} in either orientation"
                                )
                        else:
                            # Log error for image encoding failure
                            AnalysisLog.objects.create(
                                drawing=drawing,
                                message=f"Error processing segment {segment_number} - could not encode image"
                            )
                    except Exception as segment_error:
                        # Log segment processing error
                        error_message = str(segment_error)
                        AnalysisLog.objects.create(
                            drawing=drawing,
                            message=f"Error processing segment {segment_number}: {error_message}"
                        )
                        print(f"Segment processing error: {error_message}")
                        print(traceback.format_exc())

                    # Clean up temporary segment file
                    try:
                        if os.path.exists(segment_path):
                            os.remove(segment_path)
                    except Exception as cleanup_error:
                        print(f"Error cleaning up segment file: {cleanup_error}")

                # Mark drawing as processed
                drawing.processed = True
                drawing.save()

                # Log completion
                AnalysisLog.objects.create(
                    drawing=drawing,
                    message=f"Analysis completed successfully with {ai_model} model"
                )

                return Response({
                    'message': 'Analysis completed successfully',
                    'drawing_id': drawing_id
                })

            except Exception as analysis_error:
                # Log the error
                error_message = str(analysis_error)
                AnalysisLog.objects.create(
                    drawing=drawing,
                    message=f"Error during analysis: {error_message}"
                )
                print(f"Analysis error: {error_message}")
                print(traceback.format_exc())

                return Response({
                    'message': f'Error during analysis: {error_message}',
                    'drawing_id': drawing_id
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # Catch any unexpected errors
        error_message = str(e)
        print(f"Unexpected error: {error_message}")
        import traceback
        print(traceback.format_exc())

        return Response({
            'message': f'An unexpected error occurred: {error_message}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Get Drawing Results
@api_view(['GET'])
def get_drawing_results(request, drawing_id):
    drawing = get_object_or_404(Drawing, id=drawing_id)
    segments = Segment.objects.filter(drawing=drawing)

    return Response({
        'drawing': DrawingDetailSerializer(drawing).data,
        'segments': SegmentSerializer(segments, many=True).data
    })

# Get Segment Details
@api_view(['GET'])
def get_segment_details(request, segment_id):
    import logging
    from api.utils.ocr_enhancement import normalize_value

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    # Automatically clean up when segment is opened
    try:
        symbols = Symbol.objects.filter(segment=segment)

        # Step 1: Remove spaces from all symbol values
        spaces_removed_count = 0
        for symbol in symbols:
            original_value = symbol.value
            new_value = original_value.replace(' ', '')

            if original_value != new_value:
                symbol.value = new_value
                symbol.save()
                spaces_removed_count += 1
                logger.info(f"Auto-removed spaces from symbol ID {symbol.id}: '{original_value}' -> '{new_value}'")

        # Step 2: Group symbols by their normalized value for duplicate detection
        value_groups = {}
        # Refresh symbols after space removal
        symbols = Symbol.objects.filter(segment=segment)
        for symbol in symbols:
            normalized_value = normalize_value(symbol.value).lower().strip()
            if normalized_value not in value_groups:
                value_groups[normalized_value] = []
            value_groups[normalized_value].append(symbol)

        # Step 3: Find and delete duplicate orientation pairs
        deleted_count = 0
        for value, symbol_group in value_groups.items():
            if len(symbol_group) == 2:  # Must be exactly 2 symbols
                # Check orientations - handle None values properly
                orientations = []
                for s in symbol_group:
                    if s.orientation:
                        orientations.append(s.orientation)
                    else:
                        # If orientation is None, check position_source to infer orientation
                        if s.position_source == 'gpt_vision' and 'rotated' in str(s.description).lower():
                            orientations.append('rotated')
                        else:
                            orientations.append('normal')  # Default to normal

                logger.info(f"Checking duplicate group for value '{value}': orientations = {orientations}")

                # Only delete if one is normal and one is rotated
                if len(orientations) == 2 and 'normal' in orientations and 'rotated' in orientations:
                    # Find which one is normal and which is rotated
                    normal_symbol = None
                    rotated_symbol = None

                    for i, symbol in enumerate(symbol_group):
                        if orientations[i] == 'normal':
                            normal_symbol = symbol
                        elif orientations[i] == 'rotated':
                            rotated_symbol = symbol

                    # Keep normal, delete rotated
                    if normal_symbol and rotated_symbol:
                        logger.info(f"Auto-deleting rotated duplicate: keeping normal symbol ID {normal_symbol.id}, deleting rotated symbol ID {rotated_symbol.id} for value '{normal_symbol.value}'")

                        # Delete the rotated duplicate
                        rotated_symbol.delete()
                        deleted_count += 1
                else:
                    # Check for ± vs + variations (same normalized value but different raw values)
                    raw_values = [s.value for s in symbol_group]
                    # If one has ± and another has just +, delete the one with just +
                    plus_minus_symbol = None
                    plus_only_symbol = None

                    for symbol in symbol_group:
                        if '±' in symbol.value:
                            plus_minus_symbol = symbol
                        elif '+' in symbol.value and '±' not in symbol.value:
                            plus_only_symbol = symbol

                    if plus_minus_symbol and plus_only_symbol:
                        logger.info(f"Auto-deleting symbol with single + sign: keeping ± symbol ID {plus_minus_symbol.id} ('{plus_minus_symbol.value}'), deleting + symbol ID {plus_only_symbol.id} ('{plus_only_symbol.value}')")
                        plus_only_symbol.delete()
                        deleted_count += 1

        # Recreate marked image if any changes were made
        if spaces_removed_count > 0 or deleted_count > 0:
            logger.info(f"Auto-cleanup completed: removed spaces from {spaces_removed_count} symbols, deleted {deleted_count} duplicate orientation symbols from segment {segment_id}")
            recreate_marked_image(segment)

            # Log the cleanup
            cleanup_messages = []
            if spaces_removed_count > 0:
                cleanup_messages.append(f"removed spaces from {spaces_removed_count} symbol values")
            if deleted_count > 0:
                cleanup_messages.append(f"deleted {deleted_count} duplicate orientation symbols")

            AnalysisLog.objects.create(
                drawing=segment.drawing,
                message=f"Auto-cleanup when opening segment {segment.segment_number}: {', '.join(cleanup_messages)}"
            )

    except Exception as e:
        logger.error(f"Error during automatic cleanup for segment {segment_id}: {str(e)}")
        # Continue with normal response even if cleanup fails

    # Get fresh symbols after cleanup
    symbols = Symbol.objects.filter(segment=segment)

    # Step 4: Group symbols by normalized value for better organization
    try:
        # Group symbols by their normalized values
        value_groups = {}
        for symbol in symbols:
            normalized_value = normalize_value(symbol.value).lower().strip()
            if normalized_value not in value_groups:
                value_groups[normalized_value] = []
            value_groups[normalized_value].append(symbol)

        # Sort symbols: grouped by normalized value, then by ID within each group
        sorted_symbols = []
        for normalized_value in sorted(value_groups.keys()):
            group_symbols = sorted(value_groups[normalized_value], key=lambda s: s.id)
            sorted_symbols.extend(group_symbols)

        # Create serialized data in the new order
        symbols_data = SymbolSerializer(sorted_symbols, many=True).data

        logger.info(f"Grouped {len(symbols)} symbols into {len(value_groups)} value groups")

    except Exception as e:
        logger.error(f"Error grouping symbols: {str(e)}")
        # Fallback to original ordering
        symbols_data = SymbolSerializer(symbols, many=True).data

    return Response({
        'segment': SegmentDetailSerializer(segment).data,
        'symbols': symbols_data
    })

# Get Analysis Logs
@api_view(['GET'])
def get_analysis_logs(request, drawing_id):
    drawing = get_object_or_404(Drawing, id=drawing_id)
    logs = AnalysisLog.objects.filter(drawing=drawing).order_by('timestamp')

    return Response({
        'logs': AnalysisLogSerializer(logs, many=True).data
    })

# Function to recreate marked image for a segment
def recreate_marked_image(segment, start_number=1, bubble_size=50):
    """
    Create a marked image for a segment with bubbles for each marked symbol.

    Args:
        segment: The segment to create a marked image for
        start_number: The starting number for the symbols in this segment
        bubble_size: The size of the bubbles in pixels (default: 50)

    Returns:
        The number of symbols marked in this segment
    """
    from PIL import Image, ImageDraw, ImageFont
    from django.conf import settings
    import os
    from django.core.files.base import ContentFile
    from io import BytesIO
    import time

    try:
        # Get the segment image path
        segment_image_path = os.path.join(settings.MEDIA_ROOT, str(segment.image))
        if not os.path.exists(segment_image_path):
            print(f"Segment image not found: {segment_image_path}")
            return 0

        # Open the segment image
        segment_img = Image.open(segment_image_path)

        # Convert to RGB mode to support color
        if segment_img.mode != 'RGB':
            segment_img = segment_img.convert('RGB')

        # Create a copy for drawing
        marked_img = segment_img.copy()
        draw = ImageDraw.Draw(marked_img)

        # Try to load a font with size proportional to bubble size, fall back to default if not available
        # Increased font size multiplier from 0.6 to 1.0 and minimum from 12 to 20 for better visibility
        font_size = max(20, int(bubble_size * 1.0))  # Larger font size for better visibility
        try:
            # Try to load Arial Bold first for better visibility
            font = ImageFont.truetype("arialbd.ttf", font_size)
        except IOError:
            try:
                # Try regular Arial
                font = ImageFont.truetype("arial.ttf", font_size)
            except IOError:
                try:
                    # Try system fonts - Helvetica Bold
                    font = ImageFont.truetype("/System/Library/Fonts/Helvetica-Bold.ttc", font_size)
                except IOError:
                    try:
                        # Try regular Helvetica
                        font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
                    except IOError:
                        font = ImageFont.load_default()

        # Get all marked symbols for this segment
        # Order by x_coordinate and y_coordinate to ensure consistent ordering
        # Use a fresh query to ensure we have the latest data from the database

        # Get the IDs of all marked symbols
        marked_symbol_ids = list(Symbol.objects.filter(segment=segment, is_marked=True).values_list('id', flat=True))
        print(f"Found {len(marked_symbol_ids)} marked symbol IDs: {marked_symbol_ids}")

        # Now get the symbols again with a fresh query
        symbols = Symbol.objects.filter(id__in=marked_symbol_ids).order_by('x_coordinate', 'y_coordinate')

        # Convert to list to avoid queryset caching
        symbols = list(symbols)
        print(f"Drawing {len(symbols)} symbols on marked image")

        # Log symbol positions for debugging
        for symbol in symbols:
            print(f"Symbol {symbol.id}: position=({symbol.x_coordinate}, {symbol.y_coordinate}), value={symbol.value}")

        # Current symbol number
        current_number = start_number

        # Draw each symbol with its sequential number
        for symbol in symbols:
            # Use the provided bubble size (radius is half the diameter)
            circle_radius = int(bubble_size / 2)
            # Draw outer circle in blue
            draw.ellipse(
                [(symbol.x_coordinate - circle_radius, symbol.y_coordinate - circle_radius),
                 (symbol.x_coordinate + circle_radius, symbol.y_coordinate + circle_radius)],
                fill="blue",  # Fill with blue
                outline="blue",
                width=max(1, int(bubble_size * 0.03))  # Width proportional to bubble size
            )

            # Draw inner circle in blue (for better visibility)
            inner_radius = int(circle_radius * 0.9)
            draw.ellipse(
                [(symbol.x_coordinate - inner_radius, symbol.y_coordinate - inner_radius),
                 (symbol.x_coordinate + inner_radius, symbol.y_coordinate + inner_radius)],
                fill="blue",
                outline="white",
                width=max(1, int(bubble_size * 0.02))  # Width proportional to bubble size
            )

            # Draw the symbol number
            try:
                # For newer PIL versions
                left, top, right, bottom = draw.textbbox((0, 0), str(current_number), font=font)
                text_width = right - left
                text_height = bottom - top
            except AttributeError:
                # Fallback for older PIL versions
                try:
                    text_width, text_height = draw.textsize(str(current_number), font=font)
                except:
                    text_width, text_height = 15, 15  # Default fallback values

            # Draw the text directly on the blue circle with stroke for better visibility
            text_x = symbol.x_coordinate - text_width/2
            text_y = symbol.y_coordinate - text_height/2

            # Draw text stroke (outline) in black for better contrast
            stroke_width = 2
            for dx in range(-stroke_width, stroke_width + 1):
                for dy in range(-stroke_width, stroke_width + 1):
                    if dx != 0 or dy != 0:  # Don't draw at the center position
                        draw.text(
                            (text_x + dx, text_y + dy),
                            str(current_number),
                            fill="black",
                            font=font
                        )

            # Draw the main white text on top
            draw.text(
                (text_x, text_y),
                str(current_number),
                fill="white",  # White text on blue background for better visibility
                font=font
            )

            # Increment the counter
            current_number += 1

        # Save the marked image to a BytesIO object
        output = BytesIO()
        marked_img.save(output, format='PNG')
        output.seek(0)

        # If the segment already has a marked image, delete it first to avoid caching issues
        if segment.marked_image:
            try:
                # Get the old file path
                old_path = os.path.join(settings.MEDIA_ROOT, str(segment.marked_image))
                # Delete the old file if it exists
                if os.path.exists(old_path):
                    os.remove(old_path)
                    print(f"Deleted old marked image: {old_path}")
            except Exception as e:
                print(f"Error deleting old marked image: {e}")

        # Add a timestamp to the filename to prevent caching
        timestamp = int(time.time())

        # Save to the segment's marked_image field
        segment.marked_image.save(
            f"segment_{segment.segment_number}_marked_{timestamp}.png",
            ContentFile(output.read()),
            save=True
        )

        # Calculate how many symbols were processed
        symbols_count = len(symbols)  # Use len() instead of count() since symbols is a list

        print(f"Created marked image for segment {segment.segment_number} with {symbols_count} symbols starting at number {start_number}")
        return symbols_count
    except Exception as e:
        print(f"Error creating marked image: {e}")
        import traceback
        traceback.print_exc()
        return 0

# Mark Symbol
@api_view(['POST'])
def mark_symbol(request, symbol_id):
    symbol = get_object_or_404(Symbol, id=symbol_id)
    serializer = MarkSymbolSerializer(data=request.data)

    if serializer.is_valid():
        # Mark the symbol
        symbol.is_marked = True

        # Set display order if provided
        if 'display_order' in serializer.validated_data:
            symbol.display_order = serializer.validated_data['display_order']

        symbol.save()

        # For individual symbol marking, we don't need to worry about sequential numbering
        # across segments, so we use the default start_number=1
        recreate_marked_image(symbol.segment)

        return Response({
            'message': 'Symbol marked successfully',
            'symbol': SymbolSerializer(symbol).data,
            'segment': SegmentSerializer(symbol.segment).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Unmark Symbol
@api_view(['POST'])
def unmark_symbol(request, symbol_id):
    symbol = get_object_or_404(Symbol, id=symbol_id)

    # Unmark the symbol
    symbol.is_marked = False
    symbol.save()

    # For individual symbol unmarking, we don't need to worry about sequential numbering
    # across segments, so we use the default start_number=1
    recreate_marked_image(symbol.segment)

    return Response({
        'message': 'Symbol unmarked successfully',
        'symbol': SymbolSerializer(symbol).data,
        'segment': SegmentSerializer(symbol.segment).data
    })

# Update Symbol Position
@api_view(['PUT'])
def update_symbol_position(request, symbol_id):
    symbol = get_object_or_404(Symbol, id=symbol_id)
    serializer = UpdateSymbolPositionSerializer(data=request.data)

    if serializer.is_valid():
        # Update coordinates
        symbol.x_coordinate = serializer.validated_data['x_coordinate']
        symbol.y_coordinate = serializer.validated_data['y_coordinate']
        symbol.position_source = 'user'  # Set position source to 'user'
        symbol.save()

        # Recreate the marked image for the segment if the symbol is marked
        if symbol.is_marked:
            recreate_marked_image(symbol.segment)

        return Response({
            'message': 'Symbol position updated successfully',
            'symbol': SymbolSerializer(symbol).data,
            'segment': SegmentSerializer(symbol.segment).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Edit Symbol
@api_view(['PATCH'])
def edit_symbol(request, symbol_id):
    symbol = get_object_or_404(Symbol, id=symbol_id)
    serializer = EditSymbolSerializer(data=request.data)

    if serializer.is_valid():
        # Update fields if provided
        if 'value' in serializer.validated_data:
            symbol.value = serializer.validated_data['value']

        if 'symbol_type' in serializer.validated_data:
            symbol.symbol_type = serializer.validated_data['symbol_type']

        if 'description' in serializer.validated_data:
            symbol.description = serializer.validated_data['description']

        symbol.save()

        # Recreate the marked image for the segment if the symbol is marked
        if symbol.is_marked:
            recreate_marked_image(symbol.segment)

        return Response({
            'message': 'Symbol updated successfully',
            'symbol': SymbolSerializer(symbol).data,
            'segment': SegmentSerializer(symbol.segment).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Delete Symbol
@api_view(['DELETE'])
def delete_symbol(request, symbol_id):
    symbol = get_object_or_404(Symbol, id=symbol_id)
    segment = symbol.segment
    was_marked = symbol.is_marked

    # Get the position of the symbol in the list before deletion
    # This helps us understand which symbols need to be renumbered
    all_symbols = list(Symbol.objects.filter(segment=segment).order_by('id'))
    deleted_index = next((i for i, s in enumerate(all_symbols) if s.id == symbol_id), -1)

    # Delete the symbol
    symbol.delete()

    # Update the remaining symbols to ensure consistent numbering
    if was_marked:
        # Get all remaining symbols for this segment
        remaining_symbols = Symbol.objects.filter(segment=segment).order_by('id')

        # Log the renumbering operation
        print(f"Renumbering symbols after deleting symbol at position {deleted_index+1}")
        print(f"Remaining symbols: {remaining_symbols.count()}")

        # Recreate the marked image for the segment
        # For individual symbol deletion, we don't need to worry about sequential numbering
        # across segments, so we use the default start_number=1
        recreate_marked_image(segment)

        # Refresh the segment to get the updated marked image
        segment.refresh_from_db()

    # Return the updated segment data
    return Response({
        'message': 'Symbol deleted successfully',
        'segment': SegmentSerializer(segment).data,
        'symbols': SymbolSerializer(Symbol.objects.filter(segment=segment).order_by('id'), many=True).data
    })

# Bulk Delete Symbols
@api_view(['POST'])
def bulk_delete_symbols(request):
    serializer = BulkDeleteSymbolsSerializer(data=request.data)

    if serializer.is_valid():
        symbol_ids = serializer.validated_data['symbol_ids']

        # Get segments and marked status before deletion
        affected_segments = set()
        for symbol in Symbol.objects.filter(id__in=symbol_ids):
            if symbol.is_marked:
                affected_segments.add(symbol.segment)

        # Delete symbols
        Symbol.objects.filter(id__in=symbol_ids).delete()

        # Recreate marked images for affected segments
        updated_segments = {}
        for segment in affected_segments:
            # Recreate the marked image
            recreate_marked_image(segment)

            # Refresh the segment to get the updated marked image
            segment.refresh_from_db()

            # Get all remaining symbols for this segment
            remaining_symbols = Symbol.objects.filter(segment=segment).order_by('id')

            # Add to the response data
            updated_segments[segment.id] = {
                'segment': SegmentSerializer(segment).data,
                'symbols': SymbolSerializer(remaining_symbols, many=True).data
            }

        return Response({
            'message': f'Deleted {len(symbol_ids)} symbols successfully',
            'updated_segments': updated_segments
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Add Symbol to Segment
@api_view(['POST'])
def add_symbol(request, segment_id):
    segment = get_object_or_404(Segment, id=segment_id)
    serializer = AddSymbolSerializer(data=request.data)

    if serializer.is_valid():
        # Create new symbol
        symbol = Symbol.objects.create(
            segment=segment,
            value=serializer.validated_data['value'],
            symbol_type=serializer.validated_data['symbol_type'],
            x_coordinate=serializer.validated_data['x_coordinate'],
            y_coordinate=serializer.validated_data['y_coordinate'],
            description=serializer.validated_data.get('description', ''),
            is_marked=True,  # New symbols are marked by default
            position_source='user'  # Set position source to 'user'
        )

        # Recreate the marked image for the segment
        recreate_marked_image(segment)

        return Response({
            'message': 'Symbol added successfully',
            'symbol': SymbolSerializer(symbol).data,
            'segment': SegmentSerializer(segment).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Add Symbol at User Click Position
@api_view(['POST'])
def add_symbol_at_click(request, segment_id):
    """
    Add a new symbol at the position where the user clicked on the segment image.
    This endpoint:
    1. Takes the clicked coordinates (segment-relative)
    2. Optionally crops a small area around the click point
    3. Sends the crop to GPT-4.1 to identify the symbol value and type
    4. Creates a new symbol at the clicked position
    """
    import base64
    from io import BytesIO
    from PIL import Image as PILImage
    import json
    import logging
    logger = logging.getLogger(__name__)

    segment = get_object_or_404(Segment, id=segment_id)
    serializer = AddSymbolAtClickSerializer(data=request.data)

    if serializer.is_valid():
        clicked_x = serializer.validated_data['clicked_x']
        clicked_y = serializer.validated_data['clicked_y']
        ai_model = serializer.validated_data.get('ai_model', 'gpt41')

        # Default values in case AI identification fails
        identified_value = "UNKNOWN_VALUE"
        identified_type = "other"

        try:
            # Get the segment image path
            segment_image_path = segment.image.path

            # Open the segment image
            pil_segment_image = PILImage.open(segment_image_path)

            # Define crop window size (e.g., 200x200 pixels around the click)
            crop_size = 200
            half_crop = crop_size // 2

            # Calculate crop box, ensuring it's within segment bounds
            crop_left = max(0, clicked_x - half_crop)
            crop_top = max(0, clicked_y - half_crop)
            crop_right = min(pil_segment_image.width, clicked_x + half_crop)
            crop_bottom = min(pil_segment_image.height, clicked_y + half_crop)

            # Log the crop dimensions
            logger.info(f"Cropping image at ({clicked_x}, {clicked_y}) with box: {crop_left}, {crop_top}, {crop_right}, {crop_bottom}")

            if crop_left < crop_right and crop_top < crop_bottom:
                # Crop the image around the clicked point
                cropped_image = pil_segment_image.crop((crop_left, crop_top, crop_right, crop_bottom))

                # Convert cropped image to base64
                buffered = BytesIO()
                cropped_image.save(buffered, format="PNG")
                img_str_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')

                # Import utility functions
                from api.utils import call_gpt41_api

                # Call GPT-4.1 API with the cropped image
                try:
                    # Create a specialized prompt for value/type identification only
                    ai_response_text = call_gpt41_api(img_str_base64, segment.segment_number)

                    if ai_response_text:
                        # Parse the response to extract value and type
                        try:
                            response_data = json.loads(ai_response_text)

                            # Try to extract the first symbol from the response
                            symbol_info = None
                            if isinstance(response_data, dict) and 'symbols' in response_data and response_data['symbols']:
                                symbol_info = response_data['symbols'][0]
                            elif isinstance(response_data, list) and response_data:
                                symbol_info = response_data[0]
                            elif isinstance(response_data, dict) and 'value' in response_data:
                                symbol_info = response_data

                            if symbol_info:
                                identified_value = symbol_info.get('value', identified_value)
                                identified_type = symbol_info.get('symbol_type', identified_type).lower()

                                # Standardize 'text' to 'other' if needed
                                if identified_type == 'text':
                                    identified_type = 'other'

                                # Log the identified value and type
                                logger.info(f"AI identified value: '{identified_value}', type: '{identified_type}'")

                                # Create an analysis log
                                AnalysisLog.objects.create(
                                    drawing=segment.drawing,
                                    message=f"AI identified value '{identified_value}' (type: {identified_type}) for click at segment {segment.segment_number} ({clicked_x},{clicked_y})"
                                )
                            else:
                                logger.warning("No symbol information found in AI response")
                                AnalysisLog.objects.create(
                                    drawing=segment.drawing,
                                    message=f"AI could not identify symbol at segment {segment.segment_number} ({clicked_x},{clicked_y})"
                                )
                        except json.JSONDecodeError:
                            logger.error(f"Error parsing AI response: {ai_response_text[:200]}...")
                            AnalysisLog.objects.create(
                                drawing=segment.drawing,
                                message=f"Error parsing AI response for click at segment {segment.segment_number}"
                            )
                    else:
                        logger.warning("No response from AI")
                        AnalysisLog.objects.create(
                            drawing=segment.drawing,
                            message=f"No response from AI for click at segment {segment.segment_number}"
                        )
                except Exception as ai_error:
                    logger.error(f"Error calling AI: {str(ai_error)}")
                    AnalysisLog.objects.create(
                        drawing=segment.drawing,
                        message=f"Error calling AI for click at segment {segment.segment_number}: {str(ai_error)}"
                    )
            else:
                logger.warning(f"Invalid crop dimensions: {crop_left}, {crop_top}, {crop_right}, {crop_bottom}")
                AnalysisLog.objects.create(
                    drawing=segment.drawing,
                    message=f"Could not create a valid crop for click at segment {segment.segment_number}"
                )
        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            AnalysisLog.objects.create(
                drawing=segment.drawing,
                message=f"Error processing image for click at segment {segment.segment_number}: {str(e)}"
            )

        # Create the symbol with the identified or default values
        symbol = Symbol.objects.create(
            segment=segment,
            value=identified_value,
            symbol_type=identified_type,
            x_coordinate=clicked_x,
            y_coordinate=clicked_y,
            description=f"Added by user click at ({clicked_x}, {clicked_y})",
            is_marked=True,  # User-placed symbols are marked by default
            position_source='user'  # Set position source to 'user'
        )

        # Recreate the marked image for the segment
        recreate_marked_image(segment)

        return Response({
            'message': 'Symbol added successfully at clicked position',
            'symbol': SymbolSerializer(symbol).data,
            'segment': SegmentSerializer(segment).data
        }, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Find Similar Segments
@api_view(['GET'])
def find_similar_segments_view(request, segment_id):
    segment = get_object_or_404(Segment, id=segment_id)

    # Import the segment similarity utility
    from .utils.segment_similarity import find_similar_segments

    # Find similar segments from other drawings
    similar_results = find_similar_segments(segment, max_results=5, min_similarity=0.6)

    if not similar_results:
        # If no similar segments found from other drawings, fall back to segments from the same drawing
        # This is just for testing purposes when there are no other drawings
        similar_segments = Segment.objects.filter(drawing=segment.drawing).exclude(id=segment.id)

        # Create similarity records for fallback
        similarities = []
        for similar in similar_segments:
            similarity, _ = SegmentSimilarity.objects.get_or_create(
                segment=segment,
                similar_segment=similar,
                defaults={'similarity_score': 0.7}  # Fallback value
            )
            similarities.append(similarity)

        # Get symbol counts for each segment
        segment_data = SegmentSerializer(similar_segments, many=True).data
        for segment_item in segment_data:
            # Count marked symbols for this segment
            symbol_count = Symbol.objects.filter(
                segment_id=segment_item['id'],
                is_marked=True
            ).count()
            segment_item['symbol_count'] = symbol_count

        return Response({
            'similar_segments': segment_data,
            'similarities': SegmentSimilaritySerializer(similarities, many=True).data
        })

    # Extract segments and similarity objects from results
    similar_segments = [result[0] for result in similar_results]
    similarity_objects = [result[2] for result in similar_results]

    # Add similarity scores to segment serialization context
    segment_serializer = SegmentSerializer(similar_segments, many=True)
    segment_data = segment_serializer.data

    # Add similarity scores and symbol counts to segment data
    for i, segment_item in enumerate(segment_data):
        segment_item['similarity_score'] = similar_results[i][1]

        # Count marked symbols for this segment
        symbol_count = Symbol.objects.filter(
            segment_id=segment_item['id'],
            is_marked=True
        ).count()
        segment_item['symbol_count'] = symbol_count

    return Response({
        'similar_segments': segment_data,
        'similarities': SegmentSimilaritySerializer(similarity_objects, many=True).data
    })

# Helper function to get image dimensions and calculate scaling factors
def get_image_scaling_factors(current_segment, similar_segment):
    """
    Calculate scaling factors between two segment images.

    Args:
        current_segment: The target segment
        similar_segment: The source segment with positions to copy

    Returns:
        tuple: (x_scale, y_scale, current_width, current_height) or None if error
    """
    from PIL import Image
    import os
    from django.conf import settings

    # Get the paths to the segment images
    current_segment_path = os.path.join(settings.MEDIA_ROOT, str(current_segment.image))
    similar_segment_path = os.path.join(settings.MEDIA_ROOT, str(similar_segment.image))

    # Check if both images exist
    if not os.path.exists(current_segment_path):
        print(f"ERROR: Current segment image not found: {current_segment_path}")
        return None, "Current segment image not found"

    if not os.path.exists(similar_segment_path):
        print(f"ERROR: Similar segment image not found: {similar_segment_path}")
        return None, "Similar segment image not found"

    try:
        # Open the images to get their dimensions
        current_img = Image.open(current_segment_path)
        similar_img = Image.open(similar_segment_path)

        # Get the dimensions
        current_width, current_height = current_img.size
        similar_width, similar_height = similar_img.size

        # Calculate scaling factors
        x_scale = current_width / similar_width
        y_scale = current_height / similar_height

        print(f"Current segment dimensions: {current_width}x{current_height}")
        print(f"Similar segment dimensions: {similar_width}x{similar_height}")
        print(f"Scaling factors: x={x_scale:.4f}, y={y_scale:.4f}")

        # Close the images to free up resources
        current_img.close()
        similar_img.close()

        return (x_scale, y_scale, current_width, current_height), None
    except Exception as e:
        print(f"ERROR: Failed to get image dimensions: {str(e)}")
        return None, f"Failed to get image dimensions: {str(e)}"

# Apply Similar Segment
@api_view(['POST'])
def apply_similar_segment(request, segment_id, similar_id):
    """
    Apply symbol positions from a similar segment to the current segment.

    This function matches existing symbols in the current segment with symbols in the similar segment
    based on their type and value similarity. It then updates the positions of the matched symbols.

    If a symbol in the similar segment doesn't have a good match in the current segment, a new symbol
    will be created with the same value and position.

    Note: To revert to the previous implementation that always creates new symbols with positions from
    the similar segment, change the USE_MATCHING_ALGORITHM flag to False.
    """
    import time
    from django.db import transaction
    from difflib import SequenceMatcher

    # Set this to False to revert to the previous implementation
    USE_MATCHING_ALGORITHM = True

    print(f"=== APPLYING SIMILAR SEGMENT {similar_id} TO SEGMENT {segment_id} ===")

    segment = get_object_or_404(Segment, id=segment_id)
    similar_segment = get_object_or_404(Segment, id=similar_id)

    print(f"Target segment: {segment.segment_number}, Similar segment: {similar_segment.segment_number}")

    # Get symbols from similar segment
    similar_symbols = Symbol.objects.filter(segment=similar_segment, is_marked=True)
    print(f"Found {similar_symbols.count()} marked symbols in similar segment")

    # Get symbols from current segment
    current_symbols = Symbol.objects.filter(segment=segment)
    print(f"Found {current_symbols.count()} symbols in current segment")

    # Use a transaction to ensure all changes are applied atomically
    with transaction.atomic():
        # Apply to current segment (copy positions)
        symbols_updated = 0
        symbols_created = 0

        # First, unmark all symbols to start fresh
        print("Unmarking all existing symbols...")
        current_symbols.update(is_marked=False)

        # Get scaling factors for coordinate translation
        scaling_result, error_message = get_image_scaling_factors(segment, similar_segment)
        if scaling_result is None:
            return Response({
                'error': 'Image scaling error',
                'message': error_message
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        x_scale, y_scale, current_width, current_height = scaling_result

        if USE_MATCHING_ALGORITHM:
            # NEW IMPLEMENTATION: Match existing symbols with similar segment symbols
            print("Using matching algorithm to apply positions")

            # Create a list of current symbols for matching
            current_symbols_list = list(current_symbols)

            # For each symbol in the similar segment, find the best match in the current segment
            for similar_symbol in similar_symbols:
                print(f"Processing similar symbol: {similar_symbol.symbol_type} - {similar_symbol.value}")

                # Find the best matching symbol based on type and value similarity
                best_match = None
                best_score = 0

                for current_symbol in current_symbols_list:
                    # Calculate match score based on symbol type and value
                    # Type match is more important (0.7 weight)
                    type_match = 1.0 if current_symbol.symbol_type == similar_symbol.symbol_type else 0.0

                    # Calculate text similarity for the value
                    text_similarity = SequenceMatcher(None, current_symbol.value, similar_symbol.value).ratio()

                    # Combined score (weighted)
                    score = (0.7 * type_match) + (0.3 * text_similarity)

                    # If score is high enough and better than previous matches
                    if score > 0.5 and score > best_score:
                        best_score = score
                        best_match = current_symbol

                if best_match:
                    # Scale the coordinates from the similar segment to the current segment
                    scaled_x = int(round(similar_symbol.x_coordinate * x_scale))
                    scaled_y = int(round(similar_symbol.y_coordinate * y_scale))

                    # Ensure coordinates are within the bounds of the current image
                    scaled_x = max(0, min(scaled_x, current_width - 1))
                    scaled_y = max(0, min(scaled_y, current_height - 1))

                    # Update the position of the matching symbol
                    print(f"Updating symbol {best_match.id} ({best_match.value}) with position from similar symbol ({similar_symbol.value})")
                    print(f"  - Original position: ({similar_symbol.x_coordinate}, {similar_symbol.y_coordinate})")
                    print(f"  - Scaled position: ({scaled_x}, {scaled_y})")
                    print(f"  - Match score: {best_score:.2f}")

                    best_match.x_coordinate = scaled_x
                    best_match.y_coordinate = scaled_y
                    best_match.is_marked = True
                    best_match.position_source = 'similar'  # Set position source to 'similar'
                    best_match.save()

                    # Remove the matched symbol from the list to avoid duplicate matches
                    current_symbols_list.remove(best_match)

                    symbols_updated += 1
                else:
                    # If no good match found, create a new symbol with scaled coordinates
                    scaled_x = int(round(similar_symbol.x_coordinate * x_scale))
                    scaled_y = int(round(similar_symbol.y_coordinate * y_scale))

                    # Ensure coordinates are within the bounds of the current image
                    scaled_x = max(0, min(scaled_x, current_width - 1))
                    scaled_y = max(0, min(scaled_y, current_height - 1))

                    print(f"No match found for similar symbol {similar_symbol.value}, creating new symbol")
                    print(f"  - Original position: ({similar_symbol.x_coordinate}, {similar_symbol.y_coordinate})")
                    print(f"  - Scaled position: ({scaled_x}, {scaled_y})")

                    new_symbol = Symbol(
                        segment=segment,
                        symbol_type=similar_symbol.symbol_type,
                        value=similar_symbol.value,
                        x_coordinate=scaled_x,
                        y_coordinate=scaled_y,
                        is_marked=True,
                        position_source='similar',  # Set position source to 'similar'
                        description=f"No matching symbol found, copied from similar segment {similar_segment.segment_number}"
                    )
                    new_symbol.save()
                    symbols_created += 1
        else:
            # ORIGINAL IMPLEMENTATION: Create new symbols with positions from similar segment
            print("Using original algorithm (creating new symbols)")

            # Now create new symbols based on the similar segment
            for similar_symbol in similar_symbols:
                # Scale the coordinates from the similar segment to the current segment
                scaled_x = int(round(similar_symbol.x_coordinate * x_scale))
                scaled_y = int(round(similar_symbol.y_coordinate * y_scale))

                # Ensure coordinates are within the bounds of the current image
                scaled_x = max(0, min(scaled_x, current_width - 1))
                scaled_y = max(0, min(scaled_y, current_height - 1))

                # Create a new symbol with the scaled position
                print(f"Creating new symbol with position from similar symbol ({similar_symbol.value})")
                print(f"  - Original position: ({similar_symbol.x_coordinate}, {similar_symbol.y_coordinate})")
                print(f"  - Scaled position: ({scaled_x}, {scaled_y})")

                new_symbol = Symbol(
                    segment=segment,
                    symbol_type=similar_symbol.symbol_type,
                    value=similar_symbol.value,
                    x_coordinate=scaled_x,
                    y_coordinate=scaled_y,
                    is_marked=True,
                    position_source='similar',  # Set position source to 'similar'
                    description=f"Copied from similar segment {similar_segment.segment_number}"
                )
                new_symbol.save()
                symbols_created += 1

        # Refresh the current_symbols queryset to include any new symbols
        current_symbols = Symbol.objects.filter(segment=segment)
        print(f"After applying positions: {current_symbols.filter(is_marked=True).count()} marked symbols")

        print(f"Updated {symbols_updated} symbols and created {symbols_created} new symbols")

    # Commit the transaction and then recreate the marked image
    # This ensures all database changes are committed before we try to recreate the image

    # Log the action
    algorithm_type = "matching" if USE_MATCHING_ALGORITHM else "original"
    AnalysisLog.objects.create(
        drawing=segment.drawing,
        message=f"Applied symbol positions from similar segment {similar_segment.segment_number} to segment {segment.segment_number} using {algorithm_type} algorithm: {symbols_updated} existing symbols positioned, {symbols_created} new symbols created"
    )

    # Add a small delay to ensure database operations complete
    time.sleep(0.5)

    # Refresh the segment from the database to get the latest data
    segment.refresh_from_db()

    # For applying similar segments, we don't need to worry about sequential numbering
    # across segments, so we use the default start_number=1
    print("Recreating marked image...")
    symbols_count = recreate_marked_image(segment)
    print(f"Marked image recreated with {symbols_count} symbols")

    # Add another small delay to ensure file system operations complete
    time.sleep(1.0)

    # Refresh the segment again to get the updated marked_image
    segment.refresh_from_db()

    # Get updated symbols - make sure to refresh from the database
    symbols = Symbol.objects.filter(segment=segment)
    print(f"Returning {symbols.count()} symbols in response")

    # Get the full URL for the marked image
    marked_image_url = segment.marked_image.url if segment.marked_image else None
    print(f"Marked image URL: {marked_image_url}")

    return Response({
        'message': 'Symbol positions applied successfully',
        'segment': SegmentSerializer(segment).data,
        'symbols': SymbolSerializer(symbols, many=True).data,
        'timestamp': int(time.time())  # Add timestamp to help with cache busting
    })

# Preview Similar Segment
@api_view(['GET'])
def preview_similar_segment(request, segment_id, similar_id):
    segment = get_object_or_404(Segment, id=segment_id)
    similar_segment = get_object_or_404(Segment, id=similar_id)

    # Get symbols from both segments
    segment_symbols = Symbol.objects.filter(segment=segment)
    similar_symbols = Symbol.objects.filter(segment=similar_segment, is_marked=True)

    return Response({
        'segment': SegmentSerializer(segment).data,
        'similar_segment': SegmentSerializer(similar_segment).data,
        'segment_symbols': SymbolSerializer(segment_symbols, many=True).data,
        'similar_symbols': SymbolSerializer(similar_symbols, many=True).data
    })

# Confirm Segment Similarity
@api_view(['POST'])
def confirm_segment_similarity(request, segment_id):
    segment = get_object_or_404(Segment, id=segment_id)
    serializer = ConfirmSimilaritySerializer(data=request.data)

    if serializer.is_valid():
        similar_segment_id = serializer.validated_data['similar_segment_id']
        similar_segment = get_object_or_404(Segment, id=similar_segment_id)

        # Update or create similarity record
        similarity, created = SegmentSimilarity.objects.get_or_create(
            segment=segment,
            similar_segment=similar_segment,
            defaults={
                'similarity_score': serializer.validated_data.get('similarity_score', 0.0),
                'user_confirmed': serializer.validated_data['is_similar'],
                'user_rating': serializer.validated_data.get('user_rating')
            }
        )

        if not created:
            similarity.user_confirmed = serializer.validated_data['is_similar']
            similarity.user_rating = serializer.validated_data.get('user_rating', similarity.user_rating)
            similarity.save()

        return Response({
            'message': 'Similarity confirmation recorded',
            'similarity': SegmentSimilaritySerializer(similarity).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Finalize Drawing
@api_view(['POST'])
def finalize_drawing(request, drawing_id):
    drawing = get_object_or_404(Drawing, id=drawing_id)

    # Mark drawing as finalized
    drawing.finalized = True
    drawing.save()

    # Define the finalize functions directly here to avoid import issues
    import os
    import csv
    from PIL import Image, ImageDraw, ImageFont
    from django.conf import settings
    from .models import Segment, Symbol

    # First, recreate marked images for all segments with sequential numbering
    segments = Segment.objects.filter(drawing=drawing)

    # Define the segment order (2, 1, 3, 4, ...)
    segment_order = []

    # First, try to find segments with numbers 2, 1, 3, 4
    for segment_number in [2, 1, 3, 4]:
        segment = segments.filter(segment_number=segment_number).first()
        if segment:
            segment_order.append(segment)

    # Add any remaining segments in their natural order
    for segment in segments:
        if segment not in segment_order:
            segment_order.append(segment)

    # Initialize the symbol counter
    symbol_counter = 1

    # Process each segment in the specified order
    for segment in segment_order:
        # Get all marked symbols for this segment
        marked_symbols = Symbol.objects.filter(segment=segment, is_marked=True)
        if marked_symbols.exists():
            # Recreate the marked image with the current symbol counter
            symbols_count = recreate_marked_image(segment, symbol_counter)
            # Update the counter for the next segment
            symbol_counter += symbols_count

    # Generate and save the finalized image
    def generate_finalized_drawing(drawing):
        """
        Generate a finalized drawing by stitching together the segment images with bubbles.
        This approach uses the marked_image field which contains the segment images with bubbles.
        """
        # Get all segments for this drawing
        segments = Segment.objects.filter(drawing=drawing)

        # Define the segment order (2, 1, 3, 4, ...)
        segment_order = []

        # First, try to find segments with numbers 2, 1, 3, 4
        for segment_number in [2, 1, 3, 4]:
            segment = segments.filter(segment_number=segment_number).first()
            if segment:
                segment_order.append(segment)

        # Add any remaining segments in their natural order
        for segment in segments:
            if segment not in segment_order:
                segment_order.append(segment)

        print(f"Processing {len(segment_order)} segments in order: {[s.segment_number for s in segment_order]}")

        # Get the original drawing to determine the layout
        original_image_path = os.path.join(settings.MEDIA_ROOT, str(drawing.image))
        if not os.path.exists(original_image_path):
            print(f"Original drawing image not found: {original_image_path}")
            return None

        # Open the original image to get dimensions
        original_img = Image.open(original_image_path)
        width, height = original_img.size
        print(f"Original drawing dimensions: {width}x{height}")

        # Create a new blank image with the same dimensions as the original
        final_img = Image.new('RGB', (width, height), (255, 255, 255))

        # Process each segment
        for segment in segment_order:
            # First try to use the marked_image (with bubbles), fall back to regular image if not available
            if segment.marked_image:
                segment_image_path = os.path.join(settings.MEDIA_ROOT, str(segment.marked_image))
            else:
                # If no marked image exists, we need to create one
                # For now, fall back to the regular image
                segment_image_path = os.path.join(settings.MEDIA_ROOT, str(segment.image))
                print(f"Warning: No marked image found for segment {segment.segment_number}, using regular image")

            if not os.path.exists(segment_image_path):
                print(f"Segment image not found: {segment_image_path}")
                continue

            # Open the segment image
            segment_img = Image.open(segment_image_path)
            print(f"Segment {segment.segment_number} dimensions: {segment_img.size}, mode: {segment_img.mode}")

            # Convert to RGB mode to support color
            if segment_img.mode != 'RGB':
                print(f"Converting segment {segment.segment_number} from {segment_img.mode} to RGB")
                segment_img = segment_img.convert('RGB')

            # Get the segment offset
            x_offset = segment.segment_x_offset or 0
            y_offset = segment.segment_y_offset or 0

            print(f"Placing segment {segment.segment_number} at offset ({x_offset}, {y_offset})")

            # Paste the segment image at the correct position
            final_img.paste(segment_img, (x_offset, y_offset))

        # Save the finalized image
        output_path = os.path.join(settings.MEDIA_ROOT, f"finalized_{drawing.id}.png")
        print(f"Saving final image in mode: {final_img.mode}")

        # Save with high quality
        final_img.save(output_path, format='PNG', quality=100)
        print(f"Saved finalized image to {output_path}")

        # Return the relative path
        return f"finalized_{drawing.id}.png"

    # Import the utility functions from the utils.py file using importlib
    import importlib.util
    import os
    utils_file_path = os.path.join(os.path.dirname(__file__), 'utils.py')
    spec = importlib.util.spec_from_file_location("utils_file", utils_file_path)
    utils_module = importlib.util.module_from_spec(spec)

    # Add the api module to sys.modules temporarily to resolve imports
    import sys
    sys.modules['api'] = sys.modules[__name__.split('.')[0]]

    spec.loader.exec_module(utils_module)
    generate_measurement_excel = utils_module.generate_measurement_excel
    generate_finalized_drawing = utils_module.generate_finalized_drawing

    # Generate and save the finalized image
    finalized_image_path = generate_finalized_drawing(drawing)
    if finalized_image_path:
        drawing.finalized_image = finalized_image_path

    # Generate and save the measurements Excel file
    measurements_excel_path = generate_measurement_excel(drawing)
    if measurements_excel_path:
        drawing.measurements_excel = measurements_excel_path

    # Save the drawing with updated file paths
    drawing.save()

    # Log finalization
    AnalysisLog.objects.create(
        drawing=drawing,
        message="Drawing finalized with numbered bubbles and measurements Excel file"
    )

    return Response({
        'message': 'Drawing finalized successfully',
        'drawing': DrawingSerializer(drawing).data,
        'final_image': drawing.finalized_image.url if drawing.finalized_image else drawing.image.url if drawing.image else None,
        'measurements_excel': drawing.measurements_excel.url if drawing.measurements_excel else None,
        'summary_file': None  # No summary file in this demo
    })

# Create Excel Sheet (without finalizing)
@api_view(['POST'])
def create_excel_sheet(request, drawing_id):
    """
    Create an Excel sheet with measurements without finalizing the drawing.
    This allows users to generate and download the Excel file before finalization.
    """
    drawing = get_object_or_404(Drawing, id=drawing_id)

    # Import the utility function from the utils.py file using importlib
    import importlib.util
    import os
    utils_file_path = os.path.join(os.path.dirname(__file__), 'utils.py')
    spec = importlib.util.spec_from_file_location("utils_file", utils_file_path)
    utils_module = importlib.util.module_from_spec(spec)

    # Add the api module to sys.modules temporarily to resolve imports
    import sys
    sys.modules['api'] = sys.modules[__name__.split('.')[0]]

    spec.loader.exec_module(utils_module)
    generate_measurement_excel = utils_module.generate_measurement_excel

    try:
        # Generate the measurements Excel file
        measurements_excel_path = generate_measurement_excel(drawing)
        if measurements_excel_path:
            drawing.measurements_excel = measurements_excel_path
            drawing.save()

            # Log the Excel creation
            AnalysisLog.objects.create(
                drawing=drawing,
                message="Measurements Excel file created"
            )

            return Response({
                'message': 'Excel sheet created successfully',
                'measurements_excel': drawing.measurements_excel.url if drawing.measurements_excel else None,
                'drawing': DrawingSerializer(drawing).data
            })
        else:
            return Response({
                'error': 'Failed to generate Excel file'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        return Response({
            'error': f'Error creating Excel sheet: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Adjust Final Bubble Size
@api_view(['POST'])
def adjust_final_bubbles(request, drawing_id):
    drawing = get_object_or_404(Drawing, id=drawing_id)
    serializer = AdjustBubbleSizeSerializer(data=request.data)

    if serializer.is_valid():
        bubble_size = serializer.validated_data['bubble_size']

        # Log the adjustment
        AnalysisLog.objects.create(
            drawing=drawing,
            message=f"Adjusted bubble size to {bubble_size}"
        )

        # Generate a new finalized image with the adjusted bubble size
        try:
            # Import necessary modules
            import os
            from PIL import Image, ImageDraw, ImageFont
            from django.conf import settings
            from .models import Segment, Symbol
            import time

            # Load the original drawing image
            image_path = os.path.join(settings.MEDIA_ROOT, str(drawing.image))
            if not os.path.exists(image_path):
                return Response({
                    'message': 'Original image not found',
                    'error': 'Original image file does not exist'
                }, status=status.HTTP_404_NOT_FOUND)

            # Open the image with PIL
            img = Image.open(image_path)
            draw = ImageDraw.Draw(img)

            # Calculate font size based on bubble size - increased for better visibility
            font_size = max(20, int(bubble_size * 1.2))  # Larger font size, minimum 20px

            # Try to load a font, fall back to default if not available
            try:
                # Try to load Arial Bold first for better visibility
                font = ImageFont.truetype("arialbd.ttf", font_size)
            except IOError:
                try:
                    # Try regular Arial
                    font = ImageFont.truetype("arial.ttf", font_size)
                except IOError:
                    try:
                        # Try system fonts - Helvetica Bold
                        font = ImageFont.truetype("/System/Library/Fonts/Helvetica-Bold.ttc", font_size)
                    except IOError:
                        try:
                            # Try regular Helvetica
                            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
                        except IOError:
                            font = ImageFont.load_default()

            # Get all segments for this drawing
            segments = Segment.objects.filter(drawing=drawing)

            # Define the segment order (2, 1, 3, 4, ...)
            segment_order = []

            # First, try to find segments with numbers 2, 1, 3, 4
            for segment_number in [2, 1, 3, 4]:
                segment = segments.filter(segment_number=segment_number).first()
                if segment:
                    segment_order.append(segment)

            # Add any remaining segments in their natural order
            for segment in segments:
                if segment not in segment_order:
                    segment_order.append(segment)

            # Initialize a counter for symbol numbering across all segments
            symbol_counter = 1

            # Process each segment in the specified order (2, 1, 3, 4)
            for segment in segment_order:
                # Get all marked symbols for this segment
                symbols = Symbol.objects.filter(segment=segment, is_marked=True).order_by('x_coordinate', 'y_coordinate')

                # Calculate the offset for this segment
                x_offset = segment.segment_x_offset or 0
                y_offset = segment.segment_y_offset or 0

                # Draw each symbol with its sequential number
                for symbol in symbols:
                    # Calculate the absolute position in the full drawing
                    abs_x = x_offset + symbol.x_coordinate
                    abs_y = y_offset + symbol.y_coordinate

                    # Draw a circle for the symbol - using the specified bubble size
                    circle_radius = int(bubble_size / 2)  # Radius is half the diameter

                    # Draw outer circle in blue
                    draw.ellipse(
                        [(abs_x - circle_radius, abs_y - circle_radius),
                         (abs_x + circle_radius, abs_y + circle_radius)],
                        fill="blue",
                        outline="blue",
                        width=max(1, int(bubble_size * 0.05))  # Width proportional to bubble size
                    )

                    # Draw inner circle in blue with white outline for better visibility
                    inner_radius = int(circle_radius * 0.9)
                    draw.ellipse(
                        [(abs_x - inner_radius, abs_y - inner_radius),
                         (abs_x + inner_radius, abs_y + inner_radius)],
                        fill="blue",
                        outline="white",
                        width=max(1, int(bubble_size * 0.02))  # Width proportional to bubble size
                    )

                    # Draw the symbol number
                    try:
                        # For newer PIL versions
                        left, top, right, bottom = draw.textbbox((0, 0), str(symbol_counter), font=font)
                        text_width = right - left
                        text_height = bottom - top
                    except AttributeError:
                        # Fallback for older PIL versions
                        try:
                            text_width, text_height = draw.textsize(str(symbol_counter), font=font)
                        except:
                            text_width, text_height = font_size // 2, font_size // 2  # Default fallback values

                    # Draw white text with symbol number - add stroke for better visibility
                    text_x = abs_x - text_width/2
                    text_y = abs_y - text_height/2

                    # Draw text stroke (outline) in black for better contrast
                    stroke_width = max(1, int(bubble_size * 0.02))  # Proportional to bubble size
                    for dx in range(-stroke_width, stroke_width + 1):
                        for dy in range(-stroke_width, stroke_width + 1):
                            if dx != 0 or dy != 0:  # Don't draw at the center position
                                draw.text(
                                    (text_x + dx, text_y + dy),
                                    str(symbol_counter),
                                    fill="black",
                                    font=font
                                )

                    # Draw the main white text on top
                    draw.text(
                        (text_x, text_y),
                        str(symbol_counter),
                        fill="white",
                        font=font
                    )

                    # Increment the counter
                    symbol_counter += 1

            # Save the finalized image with a timestamp to avoid caching issues
            timestamp = int(time.time())
            output_filename = f"finalized_{drawing.id}_{timestamp}.png"
            output_path = os.path.join(settings.MEDIA_ROOT, output_filename)
            img.save(output_path)

            # Update the drawing record with the new image
            drawing.finalized_image = output_filename
            drawing.save()

            # Return both final_image and finalized_image for compatibility
            finalized_image_url = drawing.finalized_image.url if drawing.finalized_image else None
            return Response({
                'message': 'Bubble size adjusted successfully',
                'final_image': finalized_image_url,
                'finalized_image': finalized_image_url
            })

        except Exception as e:
            import traceback
            print(f"Error adjusting bubble size: {e}")
            print(traceback.format_exc())

            return Response({
                'message': 'Error adjusting bubble size',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Mark Drawing as Template
@api_view(['POST'])
def mark_as_template(request, drawing_id):
    drawing = get_object_or_404(Drawing, id=drawing_id)

    # Mark as template
    drawing.is_template = True
    drawing.save()

    # Log the action
    AnalysisLog.objects.create(
        drawing=drawing,
        message="Drawing marked as template"
    )

    return Response({
        'message': 'Drawing marked as template successfully',
        'drawing': DrawingSerializer(drawing).data
    })

# Company ViewSet
class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer

# CageType ViewSet
class CageTypeViewSet(viewsets.ModelViewSet):
    queryset = CageType.objects.all()
    serializer_class = CageTypeSerializer

    def get_queryset(self):
        queryset = CageType.objects.all()
        company_id = self.request.query_params.get('company_id')
        if company_id:
            queryset = queryset.filter(company_id=company_id)
        return queryset

# Template ViewSet
class TemplateViewSet(viewsets.ModelViewSet):
    queryset = Template.objects.all()
    serializer_class = TemplateSerializer

    def get_queryset(self):
        queryset = Template.objects.all()
        cage_type_id = self.request.query_params.get('cage_type_id')
        if cage_type_id:
            queryset = queryset.filter(cage_type_id=cage_type_id)
        return queryset


# Save Segment with Bubble Size
@api_view(['POST'])
def save_segment_with_bubble_size(request, segment_id):
    """
    Save a segment with a specific bubble size.
    This endpoint:
    1. Takes a bubble size parameter
    2. Recreates the marked image with the specified bubble size
    3. Returns the updated segment
    """
    segment = get_object_or_404(Segment, id=segment_id)
    serializer = AdjustBubbleSizeSerializer(data=request.data)

    if serializer.is_valid():
        bubble_size = serializer.validated_data['bubble_size']

        # Log the adjustment
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Adjusted bubble size to {bubble_size} for segment {segment.segment_number}"
        )

        # Recreate the marked image with the specified bubble size
        recreate_marked_image(segment, bubble_size=bubble_size)

        # Refresh the segment to get the updated marked image
        segment.refresh_from_db()

        return Response({
            'message': f'Segment saved with bubble size {bubble_size}',
            'segment': SegmentSerializer(segment).data,
            'symbols': SymbolSerializer(Symbol.objects.filter(segment=segment), many=True).data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Get All EasyOCR Detections
@api_view(['GET'])
def get_easyocr_detections(request, segment_id):
    """
    Get all text detected by EasyOCR in a segment from the saved database records.
    This endpoint:
    1. Retrieves all EasyOCRDetection records for the segment
    2. Returns both matched and unmatched detections
    3. Includes summary statistics
    4. Returns the existing EasyOCR debug image if available
    """
    import logging

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get all EasyOCR detections for this segment from the database
        from .models import EasyOCRDetection
        easyocr_detections = EasyOCRDetection.objects.filter(segment=segment).order_by('-confidence', 'detected_text')

        # Prepare the response data
        detections = []
        total_detected = easyocr_detections.count()
        total_matched = easyocr_detections.filter(is_matched=True).count()
        rotated_count = easyocr_detections.filter(orientation='rotated').count()

        # Process each EasyOCR detection
        for detection in easyocr_detections:
            detections.append({
                'detected_text': detection.detected_text,
                'confidence': detection.confidence,
                'center_x': detection.center_x,
                'center_y': detection.center_y,
                'normalized_x': detection.normalized_x,
                'normalized_y': detection.normalized_y,
                'orientation': detection.orientation,
                'matched_symbol': {
                    'id': detection.matched_symbol.id,
                    'value': detection.matched_symbol.value,
                    'symbol_type': detection.matched_symbol.symbol_type,
                    'symbol_type_display': detection.matched_symbol.get_symbol_type_display()
                } if detection.matched_symbol else None,
                'similarity': detection.similarity_score,
                'is_matched': detection.is_matched
            })

        # Get debug image URL if available
        debug_image_url = None
        if hasattr(segment, 'easyocr_debug_image') and segment.easyocr_debug_image:
            debug_image_url = segment.easyocr_debug_image.url

        return Response({
            'success': True,
            'debug_image_url': debug_image_url,
            'summary': {
                'total_detected': total_detected,
                'total_matched': total_matched,
                'rotated_orientation': rotated_count,
                'normal_orientation': total_detected - rotated_count
            },
            'detections': detections
        })

    except Exception as e:
        logger.error(f"Error retrieving EasyOCR detections: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)

# Find Symbol Positions with Hybrid GPT-4 Vision Approach
@api_view(['POST'])
def find_positions_with_gpt_vision(request, segment_id):
    """
    Use a hybrid approach of EasyOCR and GPT-4 Vision to find positions of symbols in a segment.
    This endpoint:
    1. Uses EasyOCR for initial text detection
    2. Creates crops around detected text
    3. Sends crops to GPT-4 Vision for accurate interpretation
    4. Matches interpreted values with existing symbols
    5. Updates positions of matched symbols
    6. Returns the updated segment with symbols and debug visualization
    """
    from api.utils.gpt_vision import process_image_with_hybrid_approach
    import logging
    import os
    from django.conf import settings
    from django.urls import reverse

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get the segment image path
        segment_image_path = segment.image.path

        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        # Enable debug mode to generate visualization
        debug_mode = True

        # Process the image with the hybrid approach
        matched_symbols, debug_image_path = process_image_with_hybrid_approach(
            segment_image_path,
            symbols,
            debug=debug_mode
        )

        # Track how many symbols were updated
        updated_count = 0

        # Update symbol positions based on matches
        for match in matched_symbols:
            symbol = match['symbol']
            center_x, center_y = match['position']
            is_vertical = match.get('is_vertical', False)

            # Log the match with detailed position information
            logger.info(f"Matched GPT-4 Vision text '{match['gpt_value']}' to symbol '{symbol.value}' with similarity {match['similarity']:.2f}")
            logger.info(f"Symbol position: ({center_x}, {center_y}), Orientation: {'VERTICAL' if is_vertical else 'HORIZONTAL'}")
            print(f"GPT-4 Vision match: '{match['gpt_value']}' → '{symbol.value}' at ({center_x}, {center_y}), {'VERTICAL' if is_vertical else 'HORIZONTAL'}")

            # Update the symbol position
            symbol.x_coordinate = center_x
            symbol.y_coordinate = center_y
            symbol.position_source = 'gpt_vision'
            symbol.is_marked = True  # Mark the symbol so it gets a bubble
            symbol.save()

            updated_count += 1

        # Recreate the marked image for the segment
        recreate_marked_image(segment)

        # Refresh the segment to get the updated marked image
        segment.refresh_from_db()

        # Create a relative URL for the debug image if it exists
        debug_image_url = None
        if debug_image_path:
            # Convert absolute path to relative URL
            relative_path = os.path.relpath(debug_image_path, settings.MEDIA_ROOT)
            debug_image_url = f"/media/{relative_path}"

        # Return the updated segment with symbols
        return Response({
            'message': f'Updated positions for {updated_count} symbols using GPT-4 Vision',
            'segment': SegmentSerializer(segment).data,
            'symbols': SymbolSerializer(Symbol.objects.filter(segment=segment), many=True).data,
            'debug_image_url': debug_image_url
        })

    except Exception as e:
        import traceback
        logger.error(f"Error in GPT-4 Vision processing: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Error in GPT-4 Vision processing: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Get All GPT-4 Vision Detections
@api_view(['GET'])
def get_gpt_vision_detections(request, segment_id):
    """
    Get all text detected by GPT-4 Vision in a segment without updating positions.
    This endpoint:
    1. Processes the segment image with the hybrid approach
    2. Returns all detected text and matches without modifying the database
    3. Includes crop images and their orientations
    """
    from api.utils.gpt_vision import process_image_with_hybrid_approach
    import logging
    import os
    from django.conf import settings

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get the segment image path
        segment_image_path = segment.image.path

        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        # Process the image with the hybrid approach but don't update positions
        matched_symbols, debug_image_path = process_image_with_hybrid_approach(
            segment_image_path,
            symbols,
            debug=True,
            batch_size=5,
            batch_delay=1.0
        )

        # Prepare the response data
        detections = []

        # Get all crop images from the debug directory
        debug_crops_dir = os.path.join(settings.MEDIA_ROOT, 'debug', 'crops')
        crop_files = []
        if os.path.exists(debug_crops_dir):
            crop_prefix = f"crop_{os.path.basename(segment_image_path)}_"
            crop_files = [f for f in os.listdir(debug_crops_dir) if f.startswith(crop_prefix)]

        # Create a map of crop URLs
        crop_urls = {}
        for crop_file in crop_files:
            crop_number = int(crop_file.split('_')[2]) if len(crop_file.split('_')) > 2 else 0
            crop_urls[crop_number] = f"/media/debug/crops/{crop_file}"

        # Process matched symbols
        # Group matches by crop number to handle multiple values per crop
        crop_matches = {}
        for match in matched_symbols:
            # Extract crop number from the match dictionary
            crop_number = match.get('crop_number', len(crop_matches) + 1)
            if crop_number not in crop_matches:
                crop_matches[crop_number] = []
            crop_matches[crop_number].append(match)

        # Create detection entries for each match
        for crop_number, matches in crop_matches.items():
            # Find the crop URL for this detection
            crop_url = crop_urls.get(crop_number, None)

            for match in matches:
                symbol = match['symbol']

                detections.append({
                    'detected_text': match['ocr_value'],
                    'gpt_value': match['gpt_value'],
                    'confidence': match['similarity'] * 100,  # Convert to percentage
                    'matched_symbol': {
                        'id': symbol.id,
                        'value': symbol.value,
                        'type': symbol.symbol_type
                    },
                    'position': match['position'],
                    'is_vertical': match.get('is_vertical', False),
                    'crop_url': crop_url,
                    'crop_number': crop_number
                })

        # Create a relative URL for the debug image if it exists
        debug_image_url = None
        if debug_image_path:
            # Convert absolute path to relative URL
            relative_path = os.path.relpath(debug_image_path, settings.MEDIA_ROOT)
            debug_image_url = f"/media/{relative_path}"

        return Response({
            'detections': detections,
            'total_detected': len(detections),
            'matched_count': len([d for d in detections if d['matched_symbol']['value'] != 'UNKNOWN_VALUE']),
            'debug_image_url': debug_image_url
        })

    except Exception as e:
        import traceback
        logger.error(f"Error in GPT-4 Vision detection: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Error in GPT-4 Vision detection: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Delete Simple Values
@api_view(['POST'])
def delete_simple_values(request, segment_id):
    """
    Delete all simple values (single digits, simple letters, boxed values) from a segment.
    This endpoint:
    1. Identifies all symbols with simple values
    2. Deletes them from the database
    3. Returns the count of deleted symbols
    """
    import logging
    import re

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        # Function to check if a value should be filtered out
        def should_filter_value(value):
            # Filter out single-digit numbers
            if re.match(r'^\d$', value):
                return True

            # Filter out simple letter values like A, B, A-A, B-B
            if re.match(r'^[A-Za-z]$', value) or re.match(r'^[A-Za-z]-[A-Za-z]$', value):
                return True

            # Filter out only single-digit boxed values like |9|, |3|, |5|
            if re.match(r'^\|[0-9A-Za-z]\|$', value):
                return True

            return False

        # Log all symbols for debugging
        logger.info(f"All symbols in segment {segment.id}:")
        for symbol in symbols:
            logger.info(f"Symbol ID: {symbol.id}, Value: '{symbol.value}', Type: {symbol.symbol_type}")

        # Find symbols to delete
        symbols_to_delete = []
        for symbol in symbols:
            # Log the regex match results for debugging
            is_single_digit = bool(re.match(r'^\d$', symbol.value))
            is_simple_letter = bool(re.match(r'^[A-Za-z]$', symbol.value) or re.match(r'^[A-Za-z]-[A-Za-z]$', symbol.value))
            is_boxed_value = bool(re.match(r'^\|[0-9A-Za-z]\|$', symbol.value))

            logger.info(f"Symbol '{symbol.value}': single_digit={is_single_digit}, simple_letter={is_simple_letter}, boxed_value={is_boxed_value}")

            if should_filter_value(symbol.value):
                symbols_to_delete.append(symbol)

        # Delete the symbols
        deleted_count = len(symbols_to_delete)
        for symbol in symbols_to_delete:
            logger.info(f"Deleting simple value symbol: {symbol.value} (ID: {symbol.id})")
            symbol.delete()

        # Recreate the marked image with the updated symbols
        if deleted_count > 0:
            recreate_marked_image(segment)

        # Log the deletion
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Deleted {deleted_count} simple value symbols from segment {segment.segment_number}"
        )

        return Response({
            'message': f'Successfully deleted {deleted_count} simple value symbols',
            'deleted_count': deleted_count
        })

    except Exception as e:
        import traceback
        logger.error(f"Error deleting simple values: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Error deleting simple values: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Delete Duplicate Orientation Symbols
@api_view(['POST'])
def delete_duplicate_orientation_symbols(request, segment_id):
    """
    Delete duplicate symbols that have the same value but different orientations or sources.
    This endpoint:
    1. Identifies groups of symbols with the same normalized value
    2. Keeps the best one based on priority (normal orientation, reliable source, positioned)
    3. Deletes the rest
    4. Returns the count of deleted symbols
    """
    import logging
    from api.utils.ocr_enhancement import normalize_value
    from django.db.models import Count

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        logger.info(f"Processing {symbols.count()} symbols for duplicate detection in segment {segment_id}")

        # Group symbols by their normalized value
        value_groups = {}

        # First, normalize all values and group them
        for symbol in symbols:
            # Normalize the value for comparison
            normalized_value = normalize_value(symbol.value).lower().strip()

            if normalized_value not in value_groups:
                value_groups[normalized_value] = []
            value_groups[normalized_value].append(symbol)

        # Find groups with exactly 2 symbols that have different orientations
        duplicate_groups = []
        for value, symbol_group in value_groups.items():
            if len(symbol_group) == 2:  # Must be exactly 2 symbols
                # Log the group for debugging
                logger.info(f"Found pair of symbols with normalized value '{value}': {[s.value for s in symbol_group]}")

                # Check orientations
                orientations = [s.orientation for s in symbol_group if s.orientation]

                # Log the orientations
                logger.info(f"Orientations for '{value}': {orientations}")

                # Only consider it a duplicate if:
                # 1. Exactly 2 symbols AND
                # 2. One is normal orientation and one is rotated orientation
                if len(orientations) == 2 and 'normal' in orientations and 'rotated' in orientations:
                    duplicate_groups.append(symbol_group)
                    logger.info(f"Added duplicate orientation pair for value '{value}': normal + rotated")
                else:
                    logger.info(f"Skipping group for '{value}': not a normal+rotated pair (orientations: {orientations})")

        # Process each duplicate pair (exactly 2 symbols: normal + rotated)
        deleted_count = 0
        for group in duplicate_groups:
            logger.info(f"Processing duplicate pair with {len(group)} symbols")

            # Since we know there are exactly 2 symbols (normal + rotated)
            # Find which one is normal and which is rotated
            normal_symbol = None
            rotated_symbol = None

            for symbol in group:
                if symbol.orientation == 'normal':
                    normal_symbol = symbol
                elif symbol.orientation == 'rotated':
                    rotated_symbol = symbol

            # Prefer to keep the normal orientation, delete the rotated one
            if normal_symbol and rotated_symbol:
                keep_symbol = normal_symbol
                delete_symbol = rotated_symbol

                # Remove spaces from the kept symbol's value for display
                keep_symbol.value = keep_symbol.value.replace(' ', '')
                keep_symbol.save()

                logger.info(f"Keeping normal orientation symbol ID {keep_symbol.id} and deleting rotated symbol ID {delete_symbol.id} for value '{keep_symbol.value}'")

                # Delete the rotated duplicate symbol
                delete_symbol.delete()
                deleted_count += 1
            else:
                logger.warning(f"Could not identify normal/rotated symbols in group: {[s.orientation for s in group]}")

        # Recreate the marked image with the updated symbols
        if deleted_count > 0:
            recreate_marked_image(segment)

            # Log the deletion
            AnalysisLog.objects.create(
                drawing=segment.drawing,
                message=f"Deleted {deleted_count} duplicate orientation symbols from segment {segment.segment_number}"
            )

        # Prepare summary information
        summary = {
            'total_symbols_processed': symbols.count(),
            'duplicate_pairs_found': len(duplicate_groups),
            'rotated_symbols_deleted': deleted_count,
            'symbols_remaining': symbols.count() - deleted_count
        }

        logger.info(f"Duplicate orientation deletion summary: {summary}")

        return Response({
            'message': f'Successfully deleted {deleted_count} rotated symbols from {len(duplicate_groups)} normal+rotated pairs',
            'deleted_count': deleted_count,
            'summary': summary
        })

    except Exception as e:
        import traceback
        logger.error(f"Error deleting duplicate orientation symbols: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Error deleting duplicate orientation symbols: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Remove Spaces from All Symbol Values
@api_view(['POST'])
def remove_spaces_from_values(request, segment_id):
    """
    Remove spaces from all symbol values in a segment.
    This endpoint:
    1. Gets all symbols for the segment
    2. Removes spaces from each symbol's value
    3. Saves the updated symbols
    4. Returns the count of updated symbols
    """
    import logging

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        if not symbols.exists():
            return Response({
                'message': 'No symbols found in this segment',
                'updated_count': 0
            })

        # Update each symbol to remove spaces
        updated_count = 0
        for symbol in symbols:
            try:
                original_value = symbol.value or ""  # Handle None values
                new_value = original_value.replace(' ', '')

                if original_value != new_value:
                    symbol.value = new_value
                    symbol.save()
                    updated_count += 1
                    logger.info(f"Updated symbol ID {symbol.id}: '{original_value}' -> '{new_value}'")
            except Exception as symbol_error:
                logger.error(f"Error updating symbol ID {symbol.id}: {str(symbol_error)}")
                continue  # Continue with other symbols

        # Recreate the marked image with the updated symbols
        if updated_count > 0:
            try:
                recreate_marked_image(segment)
            except Exception as image_error:
                logger.error(f"Error recreating marked image: {str(image_error)}")
                # Continue anyway, the space removal was successful

            # Log the update
            try:
                AnalysisLog.objects.create(
                    drawing=segment.drawing,
                    message=f"Removed spaces from {updated_count} symbol values in segment {segment.segment_number}"
                )
            except Exception as log_error:
                logger.error(f"Error creating analysis log: {str(log_error)}")

        return Response({
            'message': f'Successfully removed spaces from {updated_count} symbol values',
            'updated_count': updated_count
        })

    except Exception as e:
        import traceback
        logger.error(f"Error removing spaces from symbol values: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Failed to remove spaces from symbol values. Please try again.',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Distribute Duplicate Value Symbols
@api_view(['POST'])
def distribute_duplicate_value_symbols(request, segment_id):
    """
    Distribute symbols with the same value across different physical locations in the drawing.
    This endpoint:
    1. Identifies groups of symbols with the same value
    2. Uses EasyOCR to find all occurrences of each value in the image
    3. Distributes the symbols across these occurrences
    4. Returns the count of updated symbols
    """
    import logging
    import easyocr
    import numpy as np
    from PIL import Image
    import os
    import tempfile

    # Fix PIL image size limit to prevent decompression bomb warnings
    Image.MAX_IMAGE_PIXELS = None
    from api.utils.ocr_enhancement import normalize_value, apply_pattern_recognition
    from difflib import SequenceMatcher
    import math

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        # Group symbols by their normalized value
        value_groups = {}

        # First, normalize all values and group them
        for symbol in symbols:
            # Normalize the value for comparison
            normalized_value = normalize_value(symbol.value).lower().strip()

            if normalized_value not in value_groups:
                value_groups[normalized_value] = []
            value_groups[normalized_value].append(symbol)

        # Find groups with multiple symbols of the same value
        duplicate_groups = []
        for value, symbol_group in value_groups.items():
            if len(symbol_group) > 1:
                duplicate_groups.append((value, symbol_group))
                logger.info(f"Found {len(symbol_group)} symbols with value '{value}'")

        if not duplicate_groups:
            return Response({
                'message': 'No duplicate value symbols found to distribute',
                'updated_count': 0
            })

        # Get the segment image path
        segment_image_path = segment.image.path

        # Load and preprocess the image
        try:
            # Open the image with PIL
            pil_image = Image.open(segment_image_path)

            # Convert to RGB mode if needed
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            # Save a temporary copy in RGB format
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name
                pil_image.save(temp_path, format='PNG')

            logger.info(f"Image preprocessed and saved to temporary file: {temp_path}")

            # Use the temporary file path for EasyOCR
            segment_image_path = temp_path
        except Exception as img_error:
            logger.error(f"Error preprocessing image: {str(img_error)}")
            raise Exception(f"Failed to preprocess image: {str(img_error)}")

        # Initialize EasyOCR reader
        reader = easyocr.Reader(['en'])

        # Process the image with EasyOCR
        results = reader.readtext(segment_image_path)

        # Also process the image rotated 90 degrees clockwise to detect rotated text
        try:
            # Open the image and rotate it 90 degrees clockwise
            rotated_image = pil_image.rotate(-90, expand=True)  # -90 for clockwise rotation

            # Save the rotated image to a temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_rotated_file:
                rotated_path = temp_rotated_file.name
                rotated_image.save(rotated_path, format='PNG')

            logger.info(f"Rotated image saved to temporary file: {rotated_path}")

            # Process the rotated image with EasyOCR
            rotated_results = reader.readtext(rotated_path)

            # Clean up the temporary rotated file
            try:
                os.unlink(rotated_path)
                logger.info(f"Temporary rotated file removed: {rotated_path}")
            except Exception as cleanup_error:
                logger.warning(f"Failed to remove temporary rotated file: {str(cleanup_error)}")

            # Transform coordinates from rotated image back to original image
            original_height = pil_image.size[1]  # Get height of original image
            for detection in rotated_results:
                bbox, text, score = detection

                # Skip low confidence detections
                if score < 0.3:
                    continue

                # Calculate center point of the bounding box in rotated image
                rotated_center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                rotated_center_y = int((bbox[0][1] + bbox[2][1]) / 2)

                # Transform coordinates back to original image
                original_x = rotated_center_y
                original_y = original_height - rotated_center_x

                # Add to results with transformed coordinates
                results.append(([
                    [original_x - 10, original_y - 10],  # Approximate bounding box
                    [original_x + 10, original_y - 10],
                    [original_x + 10, original_y + 10],
                    [original_x - 10, original_y + 10]
                ], text, score))

            logger.info(f"Added {len(rotated_results)} detections from rotated image")

        except Exception as rotation_error:
            logger.error(f"Error processing rotated image: {str(rotation_error)}")
            logger.error(traceback.format_exc())

        # Clean up the temporary file
        try:
            os.unlink(temp_path)
            logger.info(f"Temporary file removed: {temp_path}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to remove temporary file: {str(cleanup_error)}")

        # Import OCR enhancement utilities
        from api.utils.ocr_enhancement import merge_nearby_detections

        # Apply proximity-based merging to combine nearby text detections
        logger.info(f"Original OCR detections: {len(results)}")
        merged_results = merge_nearby_detections(results, max_distance=50)
        logger.info(f"After merging nearby detections: {len(merged_results)}")

        # Process each duplicate group
        updated_count = 0

        for normalized_value, symbol_group in duplicate_groups:
            # Find all OCR detections that match this value
            matching_detections = []

            for detection in merged_results:
                bbox, text, score = detection

                # Skip low confidence detections
                if score < 0.3:
                    continue

                # Clean up and enhance the detected text
                detected_text = text.strip()
                enhanced_text = apply_pattern_recognition(detected_text)

                # Normalize for comparison
                normalized_detected = normalize_value(enhanced_text).lower().strip()

                # Calculate similarity
                similarity = SequenceMatcher(None, normalized_detected, normalized_value).ratio()

                # If similarity is high enough, consider it a match
                if similarity > 0.7:
                    # Calculate center point of the bounding box
                    center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                    center_y = int((bbox[0][1] + bbox[2][1]) / 2)

                    matching_detections.append({
                        'text': text,
                        'position': (center_x, center_y),
                        'similarity': similarity,
                        'bbox': bbox
                    })

                    logger.info(f"Found matching detection for '{normalized_value}': '{text}' at ({center_x}, {center_y}) with similarity {similarity:.2f}")

            # If we found matching detections
            if matching_detections:
                # Sort by similarity (highest first)
                matching_detections.sort(key=lambda x: x['similarity'], reverse=True)

                # Take the top N detections where N is the number of symbols in the group
                top_detections = matching_detections[:len(symbol_group)]

                # If we have fewer detections than symbols, use what we have
                if len(top_detections) < len(symbol_group):
                    logger.warning(f"Found only {len(top_detections)} detections for {len(symbol_group)} symbols with value '{normalized_value}'")

                # Distribute the symbols across the detections
                for i, detection in enumerate(top_detections):
                    if i < len(symbol_group):
                        symbol = symbol_group[i]

                        # Update the symbol position
                        symbol.x_coordinate = detection['position'][0]
                        symbol.y_coordinate = detection['position'][1]
                        symbol.position_source = 'distributed'  # Mark as distributed
                        symbol.is_marked = True  # Ensure it's marked
                        symbol.save()

                        logger.info(f"Updated symbol ID {symbol.id} with value '{symbol.value}' to position ({detection['position'][0]}, {detection['position'][1]})")
                        updated_count += 1
            else:
                logger.warning(f"No matching detections found for value '{normalized_value}'")

        # Recreate the marked image with the updated symbols
        if updated_count > 0:
            recreate_marked_image(segment)

            # Log the update
            AnalysisLog.objects.create(
                drawing=segment.drawing,
                message=f"Distributed {updated_count} duplicate value symbols across {segment.segment_number}"
            )

        return Response({
            'message': f'Successfully distributed {updated_count} duplicate value symbols',
            'updated_count': updated_count
        })

    except Exception as e:
        import traceback
        logger.error(f"Error distributing duplicate value symbols: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Error distributing duplicate value symbols: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Get All GPT-4.1 Detections
@api_view(['GET'])
def get_gpt41_detections(request, segment_id):
    """
    Get all values detected by GPT-4.1 in a segment and their matched symbols.
    This endpoint:
    1. Retrieves all symbols detected by GPT-4.1 for this segment
    2. Filters out single-digit numbers and simple letter values (A, B, A-A, etc.)
    3. Returns the values and their matched symbols without modifying the database
    """
    import logging
    from difflib import SequenceMatcher
    import re

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        # Prepare the response data
        detections = []

        # Group symbols by orientation
        normal_symbols = [s for s in symbols if s.orientation != 'rotated']
        rotated_symbols = [s for s in symbols if s.orientation == 'rotated']

        # Log the counts
        logger.info(f"Found {len(normal_symbols)} symbols in normal orientation and {len(rotated_symbols)} in rotated orientation")

        # Function to check if a value should be filtered out
        def should_filter_value(value):
            # Filter out single-digit numbers
            if re.match(r'^\d$', value):
                return True

            # Filter out simple letter values like A, B, A-A, B-B
            if re.match(r'^[A-Za-z]$', value) or re.match(r'^[A-Za-z]-[A-Za-z]$', value):
                return True

            # Filter out only single-digit boxed values like |9|, |3|, |5|
            if re.match(r'^\|[0-9A-Za-z]\|$', value):
                return True

            return False

        # Process symbols from both orientations
        filtered_count = 0
        for orientation, orientation_symbols in [('normal', normal_symbols), ('rotated', rotated_symbols)]:
            for symbol in orientation_symbols:
                # Skip single-digit numbers and simple letter values
                if should_filter_value(symbol.value):
                    filtered_count += 1
                    continue

                # Find if this symbol has a position (either from GPT-4.1 directly or from EasyOCR/GPT-Vision)
                has_position = symbol.position_source != 'gpt' or (symbol.x_coordinate != 0 and symbol.y_coordinate != 0)

                detections.append({
                    'value': symbol.value,
                    'type': symbol.get_symbol_type_display() or symbol.symbol_type,
                    'orientation': orientation,
                    'has_position': has_position,
                    'position_source': symbol.get_position_source_display() or symbol.position_source,
                    'is_marked': symbol.is_marked,
                    'symbol_id': symbol.id
                })

        # Log how many values were filtered out
        logger.info(f"Filtered out {filtered_count} single-digit numbers and simple letter values")

        # Sort detections by value for easier reading
        detections.sort(key=lambda x: x['value'])

        return Response({
            'detections': detections,
            'total_detected': len(detections),
            'filtered_count': filtered_count,
            'normal_count': len([d for d in detections if d['orientation'] == 'normal']),
            'rotated_count': len([d for d in detections if d['orientation'] == 'rotated']),
            'positioned_count': len([d for d in detections if d['has_position']]),
            'marked_count': len([d for d in detections if d['is_marked']])
        })

    except Exception as e:
        import traceback
        logger.error(f"Error in GPT-4.1 detection retrieval: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Error retrieving GPT-4.1 detections: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Get GPT Vision Matches
@api_view(['GET'])
def get_gpt_vision_matches(request, segment_id):
    """
    Get all text detected by GPT Vision in a segment and their matches with other symbols.
    This endpoint:
    1. Retrieves all GPT Vision detections for this segment
    2. Matches them with other symbols (GPT-4.1, EasyOCR, user-created, etc.)
    3. Returns the matches with position information
    Note: GPT Vision symbols that don't match with other symbols are considered standalone detections.
    """
    import logging
    import os
    from django.conf import settings
    from difflib import SequenceMatcher

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        # Get all GPT Vision detections from the database
        # We'll use symbols with position_source = 'gpt_vision'
        gpt_vision_symbols = symbols.filter(position_source='gpt_vision')

        # Get all GPT-4.1 symbols (position_source = 'gpt')
        gpt41_symbols = symbols.filter(position_source='gpt')

        # Prepare the response data
        detections = []
        matched_count = 0

        # Process GPT Vision detections
        for vision_symbol in gpt_vision_symbols:
            # First, look for exact matches (case-insensitive)
            exact_match_found = False
            matched_symbol = None
            max_similarity = 0

            # Check all symbols (not just GPT-4.1) for exact matches first
            all_symbols = symbols.exclude(id=vision_symbol.id)  # Exclude the current symbol

            # Helper function to normalize values for comparison
            def normalize_value(value):
                import re

                # Remove parentheses and brackets
                normalized = value.lower()
                normalized = re.sub(r'[\(\)\[\]\{\}]', '', normalized)

                # First, remove all spaces completely
                normalized = normalized.replace(" ", "")

                # Replace common variations
                normalized = normalized.replace("±", "+/-")

                # Special handling for values with trailing zeros in decimal places
                # e.g., "0.10" should match with "0.1"
                def normalize_number(match):
                    num = match.group(0)
                    if '.' in num:
                        return num.rstrip('0').rstrip('.') if '.' in num else num
                    return num

                normalized = re.sub(r'\d+\.\d+', normalize_number, normalized)

                # Handle specific case of "R0.2+0.1 0" vs "R0.2 +0.10"
                # Look for patterns like "r0.2+0.10" where the trailing 0 might be separate
                pattern = r'([r]?\d+\.\d+)([+\-]\d+\.\d+)(\d)?'
                match = re.match(pattern, normalized)

                if match:
                    # Extract the components
                    base = match.group(1)
                    tolerance = match.group(2)
                    trailing_digit = match.group(3)

                    # If there's a trailing digit, it might be part of the tolerance
                    if trailing_digit:
                        # Try to combine it with the tolerance
                        tolerance_without_zeros = tolerance.rstrip('0').rstrip('.')
                        normalized = base + tolerance_without_zeros + trailing_digit
                        logger.info(f"Normalized with trailing digit: {value} -> {normalized}")

                # Handle tolerance values with space between + and -
                # e.g., "r+0.44 -0.22" vs "r+0.44-0.22"
                tolerance_pattern = r'([r]?\d*\.?\d*)([+]\d+\.?\d*)[\s-]+(\d+\.?\d*)'
                match = re.search(tolerance_pattern, normalized)
                if match:
                    base = match.group(1)
                    pos_tolerance = match.group(2)
                    neg_tolerance = match.group(3)
                    normalized = f"{base}{pos_tolerance}-{neg_tolerance}"
                    logger.info(f"Normalized tolerance with space: {value} -> {normalized}")

                logger.info(f"Final normalized value: {value} -> {normalized}")
                return normalized

            # Normalize the vision symbol value
            normalized_vision_value = normalize_value(vision_symbol.value)

            # Log for debugging
            logger.info(f"Normalized vision value: '{vision_symbol.value}' -> '{normalized_vision_value}'")

            for symbol in all_symbols:
                # Check for exact match (case-insensitive)
                if vision_symbol.value.lower() == symbol.value.lower():
                    matched_symbol = symbol
                    max_similarity = 1.0  # 100% match
                    exact_match_found = True
                    logger.info(f"Found exact match for '{vision_symbol.value}': '{symbol.value}' (source: {symbol.position_source})")
                    break

                # Check for normalized match
                normalized_symbol_value = normalize_value(symbol.value)
                if normalized_vision_value == normalized_symbol_value:
                    matched_symbol = symbol
                    max_similarity = 0.99  # Almost 100% match
                    exact_match_found = True  # Consider it an exact match
                    logger.info(f"Found normalized match for '{vision_symbol.value}' -> '{normalized_vision_value}' with '{symbol.value}' -> '{normalized_symbol_value}' (source: {symbol.position_source})")
                    break

            # If no exact match, then look for similar matches among all symbols
            if not exact_match_found:
                for symbol in all_symbols:
                    # Calculate similarity between normalized values
                    normalized_symbol_value = normalize_value(symbol.value)

                    # Try both regular similarity and normalized similarity
                    regular_similarity = SequenceMatcher(None, vision_symbol.value.lower(), symbol.value.lower()).ratio()
                    normalized_similarity = SequenceMatcher(None, normalized_vision_value, normalized_symbol_value).ratio()

                    # Use the higher similarity score
                    similarity = max(regular_similarity, normalized_similarity)

                    # Log high similarity matches for debugging
                    if similarity > 0.6:
                        logger.info(f"Similarity between '{vision_symbol.value}' and '{symbol.value}': {similarity:.2f} (normalized: {normalized_similarity:.2f})")

                    # If this is the best match so far, update
                    if similarity > max_similarity and similarity > 0.6:  # Lower threshold to 60%
                        max_similarity = similarity
                        matched_symbol = symbol

            # Check if we found a match
            is_matched = matched_symbol is not None
            if is_matched:
                matched_count += 1

                # If we found a match and the matched symbol doesn't have a position yet, update it
                if (matched_symbol.x_coordinate == 0 and matched_symbol.y_coordinate == 0) or matched_symbol.position_source == 'gpt':
                    # Update the matched symbol with the GPT Vision position
                    matched_symbol.x_coordinate = vision_symbol.x_coordinate
                    matched_symbol.y_coordinate = vision_symbol.y_coordinate
                    matched_symbol.position_source = 'gpt_vision'
                    matched_symbol.orientation = vision_symbol.orientation
                    matched_symbol.is_marked = True  # Mark it so it gets a bubble
                    matched_symbol.save()
                    logger.info(f"Updated position for symbol '{matched_symbol.value}' from GPT Vision: ({vision_symbol.x_coordinate}, {vision_symbol.y_coordinate})")

            # Add to detections
            detections.append({
                'detected_value': vision_symbol.value,
                'matched': is_matched,
                'matched_value': matched_symbol.value if matched_symbol else None,
                'matched_type': matched_symbol.get_symbol_type_display() if matched_symbol else None,
                'position': {
                    'x': vision_symbol.x_coordinate,
                    'y': vision_symbol.y_coordinate
                },
                'orientation': vision_symbol.orientation,
                'similarity': round(max_similarity * 100) if matched_symbol else 0,
                'exact_match': exact_match_found,
                'match_source': matched_symbol.position_source if matched_symbol else None,
                'is_standalone': not is_matched,  # True if this is a standalone GPT Vision detection
                'vision_symbol_id': vision_symbol.id,  # For debugging
                'vision_symbol_type': vision_symbol.get_symbol_type_display()
            })

        # Sort detections by value for easier reading
        detections.sort(key=lambda x: x['detected_value'])

        # Count orientations
        normal_count = len([d for d in detections if d['orientation'] == 'normal'])
        rotated_count = len([d for d in detections if d['orientation'] == 'rotated'])

        # Count standalone detections
        standalone_count = len([d for d in detections if d['is_standalone']])

        # Count exact matches
        exact_matches_count = len([d for d in detections if d['exact_match']])

        # If any symbols were updated with positions, recreate the marked image
        if matched_count > 0:
            recreate_marked_image(segment)
            logger.info(f"Recreated marked image after updating {matched_count} symbol positions")

        return Response({
            'detections': detections,
            'total_detected': len(detections),
            'matched_count': matched_count,
            'standalone_count': standalone_count,
            'exact_matches_count': exact_matches_count,
            'normal_count': normal_count,
            'rotated_count': rotated_count
        })

    except Exception as e:
        import traceback
        logger.error(f"Error in GPT Vision matches retrieval: {str(e)}")
        logger.error(traceback.format_exc())

        return Response({
            'message': f'Error retrieving GPT Vision matches: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Find Symbol Positions with EasyOCR
@api_view(['POST'])
def find_positions_with_easyocr(request, segment_id):
    """
    Use EasyOCR to find positions of symbols in a segment.
    This endpoint:
    1. Processes the segment image with EasyOCR to detect text and positions
    2. Compares detected text with existing GPT-4.1 symbols
    3. Updates positions of matched symbols
    4. Returns the updated segment with symbols
    """
    import easyocr
    import numpy as np
    from PIL import Image
    import os
    import logging

    # Fix PIL image size limit to prevent decompression bomb warnings
    Image.MAX_IMAGE_PIXELS = None
    from difflib import SequenceMatcher

    logger = logging.getLogger(__name__)
    segment = get_object_or_404(Segment, id=segment_id)

    try:
        # Get the segment image path
        segment_image_path = segment.image.path

        # Load and preprocess the image to ensure it's in the correct format for EasyOCR
        try:
            # Open the image with PIL
            pil_image = Image.open(segment_image_path)

            # Convert to RGB mode if needed
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            # Save a temporary copy in RGB format
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name
                pil_image.save(temp_path, format='PNG')

            logger.info(f"Image preprocessed and saved to temporary file: {temp_path}")

            # Use the temporary file path for EasyOCR
            segment_image_path = temp_path
        except Exception as img_error:
            logger.error(f"Error preprocessing image: {str(img_error)}")
            raise Exception(f"Failed to preprocess image: {str(img_error)}")

        # Initialize EasyOCR reader
        reader = easyocr.Reader(['en'])

        # Log the start of OCR processing
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Starting EasyOCR processing for segment {segment.segment_number}"
        )

        # Process the image with EasyOCR in normal orientation
        results = reader.readtext(segment_image_path)

        # Also process the image rotated 90 degrees clockwise to detect rotated text
        try:
            # Open the image and rotate it 90 degrees clockwise
            rotated_image = pil_image.rotate(-90, expand=True)  # -90 for clockwise rotation

            # Save the rotated image to a temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_rotated_file:
                rotated_path = temp_rotated_file.name
                rotated_image.save(rotated_path, format='PNG')

            logger.info(f"Rotated image saved to temporary file: {rotated_path}")

            # Process the rotated image with EasyOCR
            rotated_results = reader.readtext(rotated_path)

            # Clean up the temporary rotated file
            try:
                os.unlink(rotated_path)
                logger.info(f"Temporary rotated file removed: {rotated_path}")
            except Exception as cleanup_error:
                logger.warning(f"Failed to remove temporary rotated file: {str(cleanup_error)}")

            # Transform coordinates from rotated image back to original image
            original_height = pil_image.size[1]  # Get height of original image
            for detection in rotated_results:
                bbox, text, score = detection

                # Skip low confidence detections
                if score < 0.3:
                    continue

                # In the rotated image, the coordinates are relative to the rotated dimensions
                # We need to transform them back to the original image coordinates

                # Calculate center point of the bounding box in rotated image
                # bbox format is [[x1,y1],[x2,y1],[x2,y2],[x1,y2]]
                rotated_center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                rotated_center_y = int((bbox[0][1] + bbox[2][1]) / 2)

                # Transform coordinates back to original image
                # For 90 degrees clockwise rotation:
                # original_x = rotated_y
                # original_y = original_width - rotated_x
                original_x = rotated_center_y
                original_y = original_height - rotated_center_x

                # Add to results with transformed coordinates
                results.append(([
                    [original_x - 10, original_y - 10],  # Approximate bounding box
                    [original_x + 10, original_y - 10],
                    [original_x + 10, original_y + 10],
                    [original_x - 10, original_y + 10]
                ], text, score))

            logger.info(f"Added {len(rotated_results)} detections from rotated image")

        except Exception as rotation_error:
            logger.error(f"Error processing rotated image: {str(rotation_error)}")
            logger.error(traceback.format_exc())

        # Clean up the temporary file
        try:
            import os
            os.unlink(temp_path)
            logger.info(f"Temporary file removed: {temp_path}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to remove temporary file: {str(cleanup_error)}")

        # Import OCR enhancement utilities
        from api.utils.ocr_enhancement import merge_nearby_detections, apply_pattern_recognition, find_best_symbol_match

        # Apply proximity-based merging to combine nearby text detections
        logger.info(f"Original OCR detections: {len(results)}")
        merged_results = merge_nearby_detections(results, max_distance=50)
        logger.info(f"After merging nearby detections: {len(merged_results)}")

        # Log the number of detected texts
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"EasyOCR detected {len(results)} text elements, merged into {len(merged_results)} in segment {segment.segment_number}"
        )

        # Clear any existing EasyOCR detections for this segment
        from .models import EasyOCRDetection
        EasyOCRDetection.objects.filter(segment=segment).delete()

        # Get image dimensions for coordinate normalization from the original segment image
        from api.utils.coordinate_normalization import get_image_dimensions, normalize_coordinates
        original_segment_path = segment.image.path
        image_width, image_height = get_image_dimensions(original_segment_path)

        if image_width <= 0 or image_height <= 0:
            logger.error(f"Could not get valid image dimensions for segment {segment.id} from {original_segment_path}")
            raise Exception("Invalid image dimensions for coordinate normalization")

        # Get all symbols for this segment
        symbols = Symbol.objects.filter(segment=segment)

        # Track how many symbols were updated
        updated_count = 0

        # Keep track of already matched symbols to handle duplicates
        already_matched_symbols = set()

        # Process each OCR result and save ALL detections to database
        for detection in merged_results:
            bbox, text, score = detection

            # Skip low confidence detections
            if score < 0.3:
                continue

            # Calculate center point of the bounding box
            # bbox format is [[x1,y1],[x2,y1],[x2,y2],[x1,y2]]
            center_x = int((bbox[0][0] + bbox[2][0]) / 2)
            center_y = int((bbox[0][1] + bbox[2][1]) / 2)
            position = (center_x, center_y)

            # Clean up and enhance the detected text
            detected_text = text.strip()

            # Apply pattern recognition to improve detection
            enhanced_text = apply_pattern_recognition(detected_text)

            # Log if pattern recognition made a change
            if enhanced_text != detected_text:
                logger.info(f"Pattern recognition: '{detected_text}' -> '{enhanced_text}'")
                detected_text = enhanced_text

            # Skip if text is too short
            if len(detected_text) < 1:
                continue

            # Determine orientation based on detection source
            # Check if this detection came from the rotated image processing
            orientation = 'normal'  # Default to normal
            # We need to track which detections came from rotated processing
            # For now, we'll use a simple heuristic based on the detection order
            # The rotated detections are added after the normal ones in the results list

            # Find the best matching symbol, considering position and already matched symbols
            # This will now use our enhanced proximity-based filtering
            best_match, best_similarity = find_best_symbol_match(
                detected_text,
                symbols,
                similarity_threshold=0.5,
                position=position,
                already_matched_symbols=already_matched_symbols
            )

            # Calculate normalized coordinates for resolution independence
            normalized_x, normalized_y = normalize_coordinates(center_x, center_y, image_width, image_height)

            # Create EasyOCRDetection record for ALL detections (matched and unmatched)
            easyocr_detection = EasyOCRDetection.objects.create(
                segment=segment,
                detected_text=detected_text,
                confidence=score,
                center_x=center_x,
                center_y=center_y,
                normalized_x=normalized_x,
                normalized_y=normalized_y,
                orientation=orientation,
                matched_symbol=best_match if best_match else None,
                similarity_score=best_similarity if best_match else 0.0,
                is_matched=best_match is not None
            )

            logger.info(f"Saved EasyOCR detection: '{detected_text}' (confidence: {score:.2f}, matched: {best_match is not None})")

            # If a good match is found, update the symbol position
            if best_match:
                # Log the match
                logger.info(f"Matched OCR text '{detected_text}' to symbol '{best_match.value}' with similarity {best_similarity:.2f}")

                # Update the symbol position with both absolute and normalized coordinates
                best_match.x_coordinate = center_x
                best_match.y_coordinate = center_y
                best_match.normalized_x = normalized_x
                best_match.normalized_y = normalized_y
                best_match.position_source = 'easyocr'
                best_match.is_marked = True  # Mark the symbol so it gets a bubble
                best_match.save()

                # Add to already matched symbols
                already_matched_symbols.add(best_match.id)

                updated_count += 1

                # Check if there are other symbols with the same value that need to be matched
                # This handles the case where multiple instances of the same symbol appear in the drawing
                similar_symbols = [
                    s for s in symbols
                    if s.id != best_match.id
                    and s.value == best_match.value
                    and s.id not in already_matched_symbols
                    and not s.is_marked  # Only consider unmarked symbols
                ]

                # Log if we found similar symbols
                if similar_symbols:
                    logger.info(f"Found {len(similar_symbols)} additional symbols with value '{best_match.value}'")

                    # Look for other detections with similar text to match with these symbols
                    for other_detection in merged_results:
                        if other_detection == detection:  # Skip the current detection
                            continue

                        other_bbox, other_text, other_score = other_detection

                        # Skip low confidence detections
                        if other_score < 0.3:
                            continue

                        # Clean and enhance the text
                        other_detected_text = other_text.strip()
                        other_enhanced_text = apply_pattern_recognition(other_detected_text)

                        if other_enhanced_text:
                            other_detected_text = other_enhanced_text

                        # Calculate similarity with the current symbol value
                        similarity = SequenceMatcher(None, other_detected_text, best_match.value).ratio()

                        # If this is a similar text and we haven't processed it yet
                        if similarity > 0.7:
                            # Calculate center point
                            other_center_x = int((other_bbox[0][0] + other_bbox[2][0]) / 2)
                            other_center_y = int((other_bbox[0][1] + other_bbox[2][1]) / 2)

                            # Find the closest unmatched similar symbol
                            closest_symbol = None
                            min_distance = float('inf')

                            for similar_symbol in similar_symbols:
                                # Skip if already matched
                                if similar_symbol.id in already_matched_symbols:
                                    continue

                                # If the symbol doesn't have a position yet, it's a good candidate
                                if similar_symbol.position_source == 'gpt' and similar_symbol.x_coordinate == 0 and similar_symbol.y_coordinate == 0:
                                    closest_symbol = similar_symbol
                                    break

                            # If we found a symbol to match
                            if closest_symbol:
                                # Calculate normalized coordinates for the additional symbol
                                other_normalized_x, other_normalized_y = normalize_coordinates(
                                    other_center_x, other_center_y, image_width, image_height
                                )

                                # Update the symbol position with both absolute and normalized coordinates
                                closest_symbol.x_coordinate = other_center_x
                                closest_symbol.y_coordinate = other_center_y
                                closest_symbol.normalized_x = other_normalized_x
                                closest_symbol.normalized_y = other_normalized_y
                                closest_symbol.position_source = 'easyocr'
                                closest_symbol.is_marked = True  # Mark the symbol
                                closest_symbol.save()

                                # Add to already matched symbols
                                already_matched_symbols.add(closest_symbol.id)

                                # Log the match
                                logger.info(f"Matched additional OCR text '{other_detected_text}' to similar symbol '{closest_symbol.value}' with similarity {similarity:.2f}")

                                updated_count += 1

                                # Remove from the list of similar symbols
                                similar_symbols.remove(closest_symbol)

        # Log completion
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"EasyOCR processing completed. Updated positions for {updated_count} symbols."
        )

        # Recreate the marked image with the updated positions
        if updated_count > 0:
            recreate_marked_image(segment)

        # Return the updated segment with symbols
        return Response({
            'message': f'EasyOCR processing completed. Updated positions for {updated_count} symbols.',
            'segment': SegmentDetailSerializer(segment).data,
            'symbols': SymbolSerializer(Symbol.objects.filter(segment=segment), many=True).data
        })

    except Exception as e:
        import traceback
        logger.error(f"Error in EasyOCR processing: {str(e)}")
        logger.error(traceback.format_exc())

        # Log the error
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Error in EasyOCR processing: {str(e)}"
        )

        return Response({
            'message': f'Error in EasyOCR processing: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Fix Symbol Positions
@api_view(['POST'])
def fix_symbol_positions_endpoint(request, segment_id):
    """
    Fix symbol positioning issues for a specific segment.
    """
    try:
        from api.utils.position_fix import fix_symbol_positions, debug_symbol_positions, validate_symbol_positions

        # Get the segment
        segment = get_object_or_404(Segment, id=segment_id)

        # Debug current positions
        debug_data = debug_symbol_positions(segment_id)

        # Validate current positions
        validation_results = validate_symbol_positions(segment_id)

        # Apply fixes
        fix_results = fix_symbol_positions(segment_id)

        # Recreate the marked image after fixes
        recreate_marked_image(segment)

        return Response({
            'message': 'Symbol positions fixed successfully',
            'debug_data': debug_data,
            'validation_results': validation_results,
            'fix_results': fix_results,
            'segment': SegmentSerializer(segment).data
        })

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error fixing symbol positions for segment {segment_id}: {str(e)}")
        return Response({
            'message': f'Error fixing symbol positions: {str(e)}',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ============================================================================
# PROCESS B VIEWS - Manual Symbol Detection Workflow
# ============================================================================

# Upload Drawing for Process B
@api_view(['POST'])
def upload_drawing_process_b(request):
    """
    Upload drawing for Process B - segmentation only, no AI analysis
    """
    try:
        print("=" * 80)
        print("UPLOAD DRAWING PROCESS B REQUEST RECEIVED")
        print("=" * 80)
        print(f"Request data keys: {request.data.keys()}")
        print(f"Request FILES: {request.FILES}")

        # Check if 'image' is in the request
        if 'image' not in request.FILES:
            print("No image file found in request")
            return Response({
                'error': 'No image file found in request',
                'message': 'Please upload an image file'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create a copy of request data and set process_type to B
        data = request.data.copy()
        data['process_type'] = 'B'

        serializer = UploadDrawingSerializer(data=data)
        if serializer.is_valid():
            try:
                # Create drawing object with Process B flag
                drawing = Drawing.objects.create(
                    image=serializer.validated_data['image'],
                    original_filename=serializer.validated_data['image'].name,
                    template=serializer.validated_data.get('template'),
                    process_type='B'  # Set Process B
                )

                # Log company and cage type information if provided
                company = serializer.validated_data.get('company')
                cage_type = serializer.validated_data.get('cage_type')

                if company:
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message=f"Company: {company.name}"
                    )

                if cage_type:
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message=f"Cage Type: {cage_type.name}"
                    )

                print(f"Drawing created with ID: {drawing.id} (Process B)")

                # Create media directories
                segments_dir = os.path.join(settings.MEDIA_ROOT, 'segments', f'drawing_{drawing.id}')
                os.makedirs(segments_dir, exist_ok=True)

                # Log progress
                AnalysisLog.objects.create(
                    drawing=drawing,
                    message=f"Drawing uploaded for Process B: {drawing.original_filename}"
                )

                return Response({
                    'drawing_id': drawing.id,
                    'process_type': 'B',
                    'message': 'Drawing uploaded successfully for Process B'
                }, status=status.HTTP_201_CREATED)

            except Exception as create_error:
                import traceback
                print(f"ERROR CREATING DRAWING: {str(create_error)}")
                print(traceback.format_exc())
                return Response({
                    'error': str(create_error),
                    'message': 'Error creating drawing'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            print(f"SERIALIZER ERRORS: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        import traceback
        print(f"ERROR IN UPLOAD_DRAWING_PROCESS_B: {str(e)}")
        print(traceback.format_exc())
        return Response({
            'error': str(e),
            'message': 'An error occurred during upload'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Segmentation Only for Process B
@api_view(['POST'])
def segment_only(request, drawing_id):
    """
    Perform segmentation only for Process B - no AI analysis
    """
    try:
        # Get drawing
        drawing = get_object_or_404(Drawing, id=drawing_id)

        # Verify this is a Process B drawing
        if drawing.process_type != 'B':
            return Response({
                'error': 'This endpoint is only for Process B drawings',
                'message': 'Use the regular analyze endpoint for Process A drawings'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Import utility functions
        from api.utils import segment_image_with_overlap

        # Log the segmentation start
        AnalysisLog.objects.create(
            drawing=drawing,
            message=f"Process B: Starting segmentation with 10% overlap for better symbol detection"
        )

        try:
            # Get the image path
            image_path = drawing.image.path

            # Log segmentation start
            AnalysisLog.objects.create(
                drawing=drawing,
                message=f"Segmenting image into 4 parts with 10% overlap"
            )

            # Segment the image with 10% overlap for Process B
            segments_info = segment_image_with_overlap(image_path, num_segments=4, overlap_percent=10)

            if not segments_info:
                AnalysisLog.objects.create(
                    drawing=drawing,
                    message="Error segmenting image - no segments created"
                )
                return Response({
                    'message': 'Error segmenting image',
                    'drawing_id': drawing_id
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Create segments in database (no symbol detection)
            for segment_info in segments_info:
                segment_number = segment_info['number']
                segment_path = segment_info['path']

                # Log segment creation
                AnalysisLog.objects.create(
                    drawing=drawing,
                    message=f"Creating segment {segment_number} (Process B - no symbol detection)"
                )

                try:
                    # Create segment in database
                    from django.core.files import File
                    import os

                    # Create a file object from the segment path
                    with open(segment_path, 'rb') as f:
                        segment_file = File(f)
                        segment_filename = f"segment_{segment_number}.png"

                        # Create segment in database with the segment image
                        segment = Segment.objects.create(
                            drawing=drawing,
                            segment_number=segment_number,
                            segment_x_offset=segment_info['x_offset'],
                            segment_y_offset=segment_info['y_offset']
                        )

                        # Save the segment image
                        segment.image.save(segment_filename, segment_file, save=True)

                    # Log segment creation success
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message=f"Segment {segment_number} created successfully (ready for manual symbol detection)"
                    )

                except Exception as segment_error:
                    # Log segment creation error
                    error_message = str(segment_error)
                    AnalysisLog.objects.create(
                        drawing=drawing,
                        message=f"Error creating segment {segment_number}: {error_message}"
                    )
                    print(f"Segment creation error: {error_message}")

                # Clean up temporary segment file
                try:
                    if os.path.exists(segment_path):
                        os.remove(segment_path)
                except Exception as cleanup_error:
                    print(f"Error cleaning up segment file: {cleanup_error}")

            # Mark drawing as processed (segmentation complete)
            drawing.processed = True
            drawing.save()

            # Log completion
            AnalysisLog.objects.create(
                drawing=drawing,
                message=f"Process B segmentation completed successfully - ready for manual symbol detection"
            )

            return Response({
                'message': 'Segmentation completed successfully',
                'drawing_id': drawing_id,
                'process_type': 'B',
                'segments_created': len(segments_info)
            })

        except Exception as segmentation_error:
            # Log the error
            error_message = str(segmentation_error)
            AnalysisLog.objects.create(
                drawing=drawing,
                message=f"Error during segmentation: {error_message}"
            )
            print(f"Segmentation error: {error_message}")

            return Response({
                'message': f'Error during segmentation: {error_message}',
                'drawing_id': drawing_id
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        # Catch any unexpected errors
        error_message = str(e)
        print(f"Unexpected error: {error_message}")
        import traceback
        print(traceback.format_exc())

        return Response({
            'message': f'An unexpected error occurred: {error_message}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Generate Bounding Boxes for Process B
@api_view(['POST'])
def generate_bounding_boxes(request, segment_id):
    """
    Generate bounding boxes using EasyOCR for Process B workflow
    """
    try:
        segment = get_object_or_404(Segment, id=segment_id)

        # Verify this is a Process B drawing
        if segment.drawing.process_type != 'B':
            return Response({
                'error': 'This endpoint is only for Process B drawings',
                'message': 'Use regular EasyOCR endpoints for Process A drawings'
            }, status=status.HTTP_400_BAD_REQUEST)

        import easyocr
        import numpy as np
        from PIL import Image
        import os

        # Fix PIL image size limit to prevent decompression bomb warnings
        Image.MAX_IMAGE_PIXELS = None

        # Log the start
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Process B: Generating bounding boxes for segment {segment.segment_number}"
        )

        # Get the segment image path
        segment_image_path = segment.image.path
        if not os.path.exists(segment_image_path):
            return Response({
                'error': 'Segment image not found',
                'message': f'Image file not found: {segment_image_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        # Initialize EasyOCR reader
        reader = easyocr.Reader(['en'])

        # Load image properly for EasyOCR
        import cv2
        import numpy as np
        from PIL import Image

        print(f"Loading image from: {segment_image_path}")

        # Load image using PIL first to ensure proper format
        pil_image = Image.open(segment_image_path)
        print(f"Original image mode: {pil_image.mode}, size: {pil_image.size}")

        # Convert to RGB if needed
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
            print(f"Converted to RGB mode")

        # Apply image preprocessing for better OCR detection
        print("🖼️ Applying image preprocessing for enhanced OCR...")

        # Convert PIL image to numpy array for preprocessing
        image_array = np.array(pil_image)
        print(f"Original image array shape: {image_array.shape}, dtype: {image_array.dtype}")

        # Ensure the array is in the correct format (uint8)
        if image_array.dtype != np.uint8:
            # Convert to uint8 if needed
            if image_array.dtype == bool:
                image_array = image_array.astype(np.uint8) * 255
            else:
                image_array = image_array.astype(np.uint8)
            print(f"Converted array dtype to: {image_array.dtype}")

        # Apply image enhancement for better OCR
        enhanced_image_array = enhance_image_for_engineering_ocr(image_array)
        print(f"Enhanced image array shape: {enhanced_image_array.shape}, dtype: {enhanced_image_array.dtype}")

        # Check if this is a small segment that might benefit from enhanced OCR
        image_width, image_height = pil_image.size
        max_dimension = max(image_width, image_height)
        is_small_segment = max_dimension < 800  # Consider segments smaller than 800px as "small"

        print(f"📏 Segment size: {image_width}x{image_height}, max dimension: {max_dimension}")
        print(f"🔍 Small segment detection: {'Yes' if is_small_segment else 'No'}")

        if is_small_segment:
            print("🚀 Using enhanced small segment OCR processing...")
            # For now, use enhanced parameters with the existing OCR system
            # TODO: Integrate full enhanced small segment OCR module later

            # Use more aggressive parameters for small segments
            results = reader.readtext(
                enhanced_image_array,
                # Even more aggressive parameters for small segments
                min_size=1,              # Absolute minimum size
                text_threshold=0.1,      # Very low threshold for small segments
                low_text=0.02,          # Extremely low threshold
                link_threshold=0.2,     # Lower linking threshold
                canvas_size=4096,       # Large canvas
                mag_ratio=2.0,          # Higher magnification for small segments
                bbox_min_score=0.02,    # Very low minimum score
                bbox_min_size=1,        # Minimum possible size
                contrast_ths=0.01,      # Very low contrast threshold
                adjust_contrast=0.1,    # Minimal contrast adjustment
                filter_ths=0.0001,      # Ultra-low filter threshold
                slope_ths=0.05,         # Very low slope threshold
                ycenter_ths=0.8,        # Higher y-center threshold
                height_ths=0.6,         # Lower height threshold for small text
                width_ths=0.8,          # Lower width threshold
                allowlist='0123456789.,±°ØøRMxABCDEFGHIJKLMNOPQRSTUVWXYZ+-=()[]{}|/\\<>~`!@#$%^&*_:;"\' μ'
            )

            print(f"📊 Enhanced small segment OCR detected {len(results)} text regions")
        else:
            print("🔧 Using standard enhanced EasyOCR parameters for engineering drawings...")
            # Standard enhanced OCR for larger segments
            results = reader.readtext(
                enhanced_image_array,
                # Detection parameters - optimized for both small and long values
                min_size=1,              # Minimum possible size to catch tiny symbols
                text_threshold=0.2,      # Even lower threshold for more detections
                low_text=0.05,          # Extremely low threshold to catch weak text
                link_threshold=0.4,     # Higher linking to better connect longer values

                # Canvas and magnification - better for high-res images
                canvas_size=4096,       # Larger canvas for high-res images (default: 2560)
                mag_ratio=1.8,          # Higher magnification for better detection (increased from 1.5)

                # Bounding box parameters - extremely sensitive for tiny symbols
                bbox_min_score=0.05,    # Extremely low minimum score for bounding boxes
                bbox_min_size=1,        # Minimum possible size for bounding boxes

                # Recognition parameters - optimized for longer text
                contrast_ths=0.02,      # Even lower contrast threshold for weak text
                adjust_contrast=0.15,   # Minimal contrast adjustment to preserve detail
                filter_ths=0.0003,      # Very low filter threshold for maximum sensitivity

                # Text detection parameters for longer values
                slope_ths=0.1,          # Lower slope threshold to catch angled text
                ycenter_ths=0.7,        # Higher y-center threshold for better text line detection
                height_ths=0.7,         # Height threshold for text detection
                width_ths=0.9,          # Width threshold for longer text sequences

                # Character allowlist for engineering symbols (expanded with μ for micro)
                allowlist='0123456789.,±°ØøRMxABCDEFGHIJKLMNOPQRSTUVWXYZ+-=()[]{}|/\\<>~`!@#$%^&*_:;"\' μ'
            )

            print(f"📊 Standard enhanced EasyOCR detected {len(results)} text regions")

        # Debug: Print all detected text for analysis
        print("🔍 All OCR detections:")
        for i, result in enumerate(results):
            bbox, text, confidence = result
            print(f"  {i+1}. Text: '{text}' | Confidence: {confidence:.3f}")

        # Clear existing bounding boxes for this segment
        BoundingBox.objects.filter(segment=segment).delete()

        # Process results and create temporary bounding box data
        temp_bounding_boxes = []

        # Adjust confidence threshold based on segment size
        if is_small_segment:
            confidence_threshold = 0.005  # Very low threshold for small segments
            print(f"🔍 Using ultra-low confidence threshold ({confidence_threshold}) for small segment")
        else:
            confidence_threshold = 0.01   # Standard low threshold for larger segments
            print(f"🔍 Using standard low confidence threshold ({confidence_threshold}) for larger segment")

        for result in results:
            bbox, text, confidence = result

            # Skip only extremely low confidence detections with adaptive threshold
            if confidence < confidence_threshold:
                print(f"⚠️ Skipping low confidence detection: '{text}' (confidence: {confidence:.3f}, threshold: {confidence_threshold})")
                continue

            print(f"✅ Accepting detection: '{text}' (confidence: {confidence:.3f})")

            # Calculate bounding box coordinates
            # bbox is a list of 4 points: [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]

            x = int(min(x_coords))
            y = int(min(y_coords))
            width = int(max(x_coords) - min(x_coords))
            height = int(max(y_coords) - min(y_coords))

            center_x = x + width // 2
            center_y = y + height // 2

            # Add to temporary list for merging
            temp_bounding_boxes.append({
                'x': x,
                'y': y,
                'width': width,
                'height': height,
                'center_x': center_x,
                'center_y': center_y,
                'confidence': confidence,
                'detected_text': text
            })

        # Filter segment-specific margins and then merge overlapping boxes
        from api.utils.process_b_gpt_vision import filter_segment_margins, merge_overlapping_bounding_boxes
        from PIL import Image

        # Get image dimensions and segment number for filtering
        segment_image_path = segment.image.path
        with Image.open(segment_image_path) as img:
            image_width = img.width
            image_height = img.height

        segment_number = segment.segment_number

        # Get custom margins from request data if provided
        custom_margins = request.data.get('custom_margins', {})
        print(f"🎯 Custom margins received: {custom_margins}")

        # Filter out segment-specific margin bounding boxes
        print(f"🎯 Before margin filtering: {len(temp_bounding_boxes)} boxes")
        filtered_boxes = filter_segment_margins(
            temp_bounding_boxes,
            image_width,
            image_height,
            segment_number,
            custom_margins=custom_margins
        )
        print(f"🎯 After margin filtering: {len(filtered_boxes)} boxes")

        # Merge overlapping/nearby bounding boxes (enhanced merging for engineering measurements)
        print(f"🔗 Before merging: {len(filtered_boxes)} boxes")
        merged_boxes = merge_overlapping_bounding_boxes(filtered_boxes, merge_threshold=25)
        print(f"🔗 After merging: {len(merged_boxes)} boxes")

        # Create final bounding box records
        bounding_boxes_created = 0
        for merged_box in merged_boxes:
            BoundingBox.objects.create(
                segment=segment,
                x=merged_box['x'],
                y=merged_box['y'],
                width=merged_box['width'],
                height=merged_box['height'],
                center_x=merged_box['center_x'],
                center_y=merged_box['center_y'],
                confidence=merged_box['confidence'],
                detected_text=merged_box['detected_text']
            )
            bounding_boxes_created += 1

        # Log completion with filtering and merging info
        original_count = len(temp_bounding_boxes)
        filtered_count = len(filtered_boxes)
        margin_filtered_count = original_count - filtered_count

        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Process B: Generated {bounding_boxes_created} bounding boxes for segment {segment.segment_number} (filtered {margin_filtered_count} margin boxes, merged from {filtered_count} detections)"
        )

        return Response({
            'message': f'Generated {bounding_boxes_created} bounding boxes',
            'bounding_boxes_count': bounding_boxes_created,
            'segment_id': segment_id
        })

    except Exception as e:
        import traceback
        print(f"Error generating bounding boxes: {str(e)}")
        print(traceback.format_exc())

        # Log the error
        if 'segment' in locals():
            AnalysisLog.objects.create(
                drawing=segment.drawing,
                message=f"Process B: Error generating bounding boxes: {str(e)}"
            )

        return Response({
            'error': str(e),
            'message': 'Error generating bounding boxes'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Get Bounding Boxes for Process B
@api_view(['GET'])
def get_bounding_boxes(request, segment_id):
    """
    Get all bounding boxes for a segment in Process B
    """
    try:
        segment = get_object_or_404(Segment, id=segment_id)

        # Verify this is a Process B drawing
        if segment.drawing.process_type != 'B':
            return Response({
                'error': 'This endpoint is only for Process B drawings'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get all bounding boxes for this segment
        bounding_boxes = BoundingBox.objects.filter(segment=segment)

        return Response({
            'bounding_boxes': BoundingBoxSerializer(bounding_boxes, many=True).data,
            'count': bounding_boxes.count()
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'message': 'Error retrieving bounding boxes'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Process Single Bounding Box with GPT Vision
@api_view(['POST'])
def process_bounding_box(request, segment_id, box_id):
    """
    Process a single bounding box with GPT Vision for Process B
    """
    try:
        segment = get_object_or_404(Segment, id=segment_id)
        bounding_box = get_object_or_404(BoundingBox, id=box_id, segment=segment)

        # Verify this is a Process B drawing
        if segment.drawing.process_type != 'B':
            return Response({
                'error': 'This endpoint is only for Process B drawings'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if already processed
        if bounding_box.processed:
            return Response({
                'message': 'Bounding box already processed',
                'bounding_box': BoundingBoxSerializer(bounding_box).data
            })

        from api.utils.process_b_gpt_vision import process_bounding_box_with_gpt_vision
        from PIL import Image
        import os

        # Log the start
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Process B: Processing bounding box {box_id} with GPT Vision"
        )

        # Get the segment image
        segment_image_path = segment.image.path
        if not os.path.exists(segment_image_path):
            return Response({
                'error': 'Segment image not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Open the image and crop the bounding box area
        with Image.open(segment_image_path) as img:
            # Crop the bounding box area with 100px padding for better LLM analysis
            padding = 100  # Increased from 10px to 100px as requested
            left = max(0, bounding_box.x - padding)
            top = max(0, bounding_box.y - padding)
            right = min(img.width, bounding_box.x + bounding_box.width + padding)
            bottom = min(img.height, bounding_box.y + bounding_box.height + padding)

            cropped_img = img.crop((left, top, right, bottom))

            # Determine orientation based on dimensions
            orientation = 'rotated' if bounding_box.height > bounding_box.width else 'normal'

            # If rotated, rotate the image 90 degrees clockwise
            if orientation == 'rotated':
                cropped_img = cropped_img.rotate(-90, expand=True)

        # Process with GPT Vision
        result = process_bounding_box_with_gpt_vision(cropped_img, bounding_box.detected_text)

        # Mark as processed
        bounding_box.processed = True
        bounding_box.save()

        # Log completion
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Process B: Processed bounding box {box_id}, detected: {result.get('value', 'unknown')}"
        )

        return Response({
            'message': 'Bounding box processed successfully',
            'bounding_box': BoundingBoxSerializer(bounding_box).data,
            'gpt_vision_result': result,
            'orientation': result.get('orientation', 'unknown')
        })

    except Exception as e:
        import traceback
        print(f"Error processing bounding box: {str(e)}")
        print(traceback.format_exc())

        return Response({
            'error': str(e),
            'message': 'Error processing bounding box'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Process Multiple Bounding Boxes in Parallel
@api_view(['POST'])
def process_multiple_bounding_boxes_parallel(request, segment_id):
    """
    Process multiple bounding boxes in parallel for faster processing in Process B
    """
    try:
        segment = get_object_or_404(Segment, id=segment_id)

        # Verify this is a Process B drawing
        if segment.drawing.process_type != 'B':
            return Response({
                'error': 'This endpoint is only for Process B drawings'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get all unprocessed bounding boxes
        bounding_boxes = BoundingBox.objects.filter(segment=segment, processed=False)

        if not bounding_boxes.exists():
            return Response({
                'message': 'No unprocessed bounding boxes found',
                'processed_count': 0
            })

        from api.utils.process_b_gpt_vision import process_multiple_bounding_boxes_sync
        import os

        # Get processing parameters from request
        batch_size = request.data.get('batch_size', 5)  # Default: 5 boxes in parallel
        delay_between_batches = request.data.get('delay_between_batches', 2)  # Default: 2 seconds

        # Convert to integers and validate parameters
        try:
            batch_size = int(batch_size)
            delay_between_batches = int(delay_between_batches)
        except (ValueError, TypeError):
            batch_size = 5
            delay_between_batches = 2

        batch_size = max(1, min(batch_size, 10))  # Limit between 1-10
        delay_between_batches = max(1, min(delay_between_batches, 10))  # Limit between 1-10 seconds

        # Log the start
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Process B: Starting parallel processing of {bounding_boxes.count()} boxes (batch_size={batch_size}, delay={delay_between_batches}s)"
        )

        # Get the segment image path
        segment_image_path = segment.image.path
        if not os.path.exists(segment_image_path):
            return Response({
                'error': 'Segment image not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Process all boxes in parallel
        results = process_multiple_bounding_boxes_sync(
            list(bounding_boxes),
            segment_image_path,
            batch_size=batch_size,
            delay_between_batches=delay_between_batches
        )

        # Mark all boxes as processed and store results
        processed_count = 0
        for result in results:
            try:
                box = BoundingBox.objects.get(id=result['box_id'])
                box.processed = True
                box.save()
                processed_count += 1
            except BoundingBox.DoesNotExist:
                continue

        # Log completion
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Process B: Parallel processing complete. Processed {processed_count} boxes successfully."
        )

        return Response({
            'message': f'Parallel processing complete',
            'processed_count': processed_count,
            'total_boxes': len(results),
            'batch_size': batch_size,
            'delay_between_batches': delay_between_batches,
            'results': results
        })

    except Exception as e:
        import traceback
        print(f"Error in parallel processing: {str(e)}")
        print(traceback.format_exc())

        return Response({
            'error': str(e),
            'message': 'Error in parallel processing'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Confirm Symbol from Bounding Box
@api_view(['POST'])
def confirm_symbol_from_bounding_box(request, segment_id):
    """
    Confirm and create a symbol from a processed bounding box in Process B
    """
    try:
        segment = get_object_or_404(Segment, id=segment_id)

        # Verify this is a Process B drawing
        if segment.drawing.process_type != 'B':
            return Response({
                'error': 'This endpoint is only for Process B drawings'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = ProcessBSymbolConfirmationSerializer(data=request.data)
        if serializer.is_valid():
            bounding_box_id = serializer.validated_data['bounding_box_id']
            confirmed = serializer.validated_data['confirmed']

            bounding_box = get_object_or_404(BoundingBox, id=bounding_box_id, segment=segment)

            if confirmed:
                # Create symbol from bounding box
                symbol_value = serializer.validated_data.get('symbol_value', bounding_box.detected_text)
                symbol_type = serializer.validated_data.get('symbol_type', 'other')
                description = serializer.validated_data.get('description', '')

                # Get positioning data for multiple values
                orientation = serializer.validated_data.get('orientation', 'normal')
                value_index = serializer.validated_data.get('value_index', 1)
                total_values = serializer.validated_data.get('total_values', 1)

                # Calculate bubble position with offset for multiple values
                base_x = bounding_box.center_x
                base_y = bounding_box.center_y

                # Apply offset for multiple values to prevent overlapping
                if total_values > 1 and value_index > 1:
                    offset_pixels = 20  # 20px offset between bubbles
                    offset_amount = (value_index - 1) * offset_pixels

                    if orientation == 'rotated_90':
                        # For rotated text, values are typically stacked vertically
                        # So offset in Y direction (downward)
                        final_x = base_x
                        final_y = base_y + offset_amount
                        print(f"🔄 Rotated orientation: Offsetting bubble {value_index}/{total_values} by {offset_amount}px in Y direction")
                    else:
                        # For normal text, values are typically left-to-right
                        # So offset in X direction (rightward)
                        final_x = base_x + offset_amount
                        final_y = base_y
                        print(f"📐 Normal orientation: Offsetting bubble {value_index}/{total_values} by {offset_amount}px in X direction")
                else:
                    # Single value or first value - use original position
                    final_x = base_x
                    final_y = base_y
                    print(f"🎯 Using original position for value {value_index}/{total_values}")

                # Create the symbol
                symbol = Symbol.objects.create(
                    segment=segment,
                    symbol_type=symbol_type,
                    value=symbol_value,
                    description=description,
                    x_coordinate=final_x,
                    y_coordinate=final_y,
                    is_marked=True,
                    position_source='user'  # User confirmed
                )

                # Link the bounding box to the symbol
                bounding_box.confirmed_symbol = symbol
                bounding_box.save()

                # Log the confirmation
                AnalysisLog.objects.create(
                    drawing=segment.drawing,
                    message=f"Process B: Symbol confirmed - {symbol_value} at ({bounding_box.center_x}, {bounding_box.center_y})"
                )

                return Response({
                    'message': 'Symbol confirmed and created',
                    'symbol': SymbolSerializer(symbol).data,
                    'bounding_box': BoundingBoxSerializer(bounding_box).data
                })
            else:
                # User rejected this detection
                AnalysisLog.objects.create(
                    drawing=segment.drawing,
                    message=f"Process B: Symbol rejected - {bounding_box.detected_text}"
                )

                return Response({
                    'message': 'Symbol rejected',
                    'bounding_box': BoundingBoxSerializer(bounding_box).data
                })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        import traceback
        print(f"Error confirming symbol: {str(e)}")
        print(traceback.format_exc())

        return Response({
            'error': str(e),
            'message': 'Error confirming symbol'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Enhanced Process B Symbol Confirmation with Decision Learning
@api_view(['POST'])
def confirm_symbols_with_learning(request, segment_id):
    """
    Enhanced symbol confirmation that records decisions for pattern learning
    Handles multiple symbols with spatial analysis and automation recommendations
    """
    try:
        segment = get_object_or_404(Segment, id=segment_id)

        # Verify this is a Process B drawing
        if segment.drawing.process_type != 'B':
            return Response({
                'error': 'This endpoint is only for Process B drawings'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Import decision learning service
        from .services.decision_learning_service import DecisionLearningService
        import time

        # Parse request data
        data = request.data
        symbols_data = data.get('symbols', [])  # List of detected symbols
        gpt_spatial_analysis = data.get('spatial_analysis', {})  # GPT Vision spatial analysis
        user_decisions = data.get('user_decisions', [])  # User's decisions for each symbol
        decision_type = data.get('decision_type', 'ACCEPT_SINGLE')  # Overall decision type
        processing_start_time = data.get('processing_start_time', time.time())

        # Calculate processing time
        processing_time = time.time() - processing_start_time

        # Get automation recommendation before user decision
        automation_recommendation = DecisionLearningService.get_automation_recommendation(
            template=segment.drawing.template,
            symbol_types=[s.get('symbol_type', 'other') for s in symbols_data],
            spatial_analysis=gpt_spatial_analysis,
            user=request.user if hasattr(request, 'user') else None
        )

        # Process user decisions and create symbols
        created_symbols = []
        for i, symbol_data in enumerate(symbols_data):
            user_decision = user_decisions[i] if i < len(user_decisions) else {}

            if user_decision.get('accepted', False):
                # Create symbol
                symbol = Symbol.objects.create(
                    segment=segment,
                    symbol_type=symbol_data.get('symbol_type', 'other'),
                    value=user_decision.get('final_value', symbol_data.get('value', '')),
                    description=symbol_data.get('description', ''),
                    x_coordinate=symbol_data.get('x', 0),
                    y_coordinate=symbol_data.get('y', 0),
                    is_marked=True,
                    position_source='user_confirmed'
                )
                created_symbols.append(symbol)

        # Record decision for learning
        decision_data = {
            'decision_type': decision_type,
            'symbols': user_decisions,
            'merge_pattern': data.get('merge_pattern', {}),
            'notes': data.get('notes', '')
        }

        decision_record = DecisionLearningService.record_user_decision(
            user=request.user if hasattr(request, 'user') else None,
            drawing=segment.drawing,
            segment=segment,
            symbols_data=symbols_data,
            gpt_analysis=gpt_spatial_analysis,
            decision_data=decision_data,
            processing_time=processing_time
        )

        # Log the decision
        AnalysisLog.objects.create(
            drawing=segment.drawing,
            message=f"Process B: Enhanced confirmation - {len(created_symbols)} symbols created, decision recorded for learning"
        )

        return Response({
            'message': 'Symbols processed with decision learning',
            'symbols_created': len(created_symbols),
            'symbols': [SymbolSerializer(s).data for s in created_symbols],
            'automation_recommendation': automation_recommendation,
            'decision_recorded': decision_record is not None,
            'processing_time': processing_time
        })

    except Exception as e:
        import traceback
        print(f"Error in enhanced symbol confirmation: {str(e)}")
        print(traceback.format_exc())

        return Response({
            'error': str(e),
            'message': 'Error in enhanced symbol confirmation'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)