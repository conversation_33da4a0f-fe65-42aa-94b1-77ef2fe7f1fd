# Generated by Django 5.2 on 2025-06-05 19:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserDecisionRecord',
            fields=[
                ('decision_id', models.AutoField(primary_key=True, serialize=False)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('decision_type', models.CharField(choices=[('ACCEPT_SINGLE', 'Accept Single Symbol'), ('REJECT_SINGLE', 'Reject Single Symbol'), ('MERGE_ALL', 'Merge All Symbols'), ('MERGE_PARTIAL', 'Merge Some Symbols'), ('REJECT_ALL', 'Reject All Symbols')], max_length=20)),
                ('processing_time', models.FloatField(help_text='Time taken to make decision in seconds')),
                ('user_notes', models.TextField(blank=True, help_text='Optional user comments')),
                ('gpt_recommended_action', models.CharField(blank=True, max_length=20)),
                ('gpt_reasoning', models.TextField(blank=True)),
                ('cage_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.cagetype')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.company')),
                ('drawing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.drawing')),
                ('segment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.segment')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.template')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_decision_records',
            },
        ),
        migrations.CreateModel(
            name='SymbolGroupAnalysis',
            fields=[
                ('group_id', models.AutoField(primary_key=True, serialize=False)),
                ('total_symbols_detected', models.IntegerField()),
                ('symbols_accepted', models.IntegerField()),
                ('symbols_rejected', models.IntegerField()),
                ('merge_pattern', models.JSONField(help_text='Which symbols were merged together')),
                ('symbols_on_same_line', models.BooleanField()),
                ('horizontal_alignment', models.CharField(max_length=20)),
                ('vertical_alignment', models.CharField(max_length=20)),
                ('symbol_grouping', models.CharField(max_length=30)),
                ('gpt_recommended_action', models.CharField(max_length=20)),
                ('gpt_reasoning', models.TextField()),
                ('bounding_box_area_percent', models.FloatField(help_text='% of segment area covered')),
                ('average_distance_normalized', models.FloatField(help_text='Average distance as % of segment diagonal')),
                ('distance_consistency', models.FloatField(help_text='How uniform the spacing is (0-1)')),
                ('spatial_stability', models.FloatField(help_text='How stable the bounding boxes are (0-1)')),
                ('context_consistency', models.FloatField(help_text='How well symbols fit engineering patterns (0-1)')),
                ('isolation_score', models.FloatField(help_text='How isolated/clear the symbols are (0-1)')),
                ('size_consistency', models.FloatField(help_text='How consistent symbol sizes are (0-1)')),
                ('decision_record', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='group_analysis', to='api.userdecisionrecord')),
            ],
            options={
                'db_table': 'symbol_group_analysis',
            },
        ),
        migrations.CreateModel(
            name='UserLearningProfile',
            fields=[
                ('profile_id', models.AutoField(primary_key=True, serialize=False)),
                ('decision_consistency_score', models.FloatField(help_text='How consistent user decisions are (0-1)')),
                ('preferred_merge_distance', models.FloatField(help_text="User's typical merge threshold")),
                ('confidence_tolerance', models.FloatField(help_text='Minimum confidence user accepts')),
                ('processing_speed', models.FloatField(help_text='Average decision time in seconds')),
                ('expertise_level', models.FloatField(help_text='Calculated expertise based on consistency and speed')),
                ('total_decisions', models.IntegerField(default=0)),
                ('merge_rate', models.FloatField(default=0.0, help_text='% of decisions that were merges')),
                ('accuracy_with_gpt_recommendations', models.FloatField(default=0.0, help_text='% agreement with GPT recommendations')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='api.template')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_learning_profiles',
            },
        ),
        migrations.CreateModel(
            name='TemplateDecisionPattern',
            fields=[
                ('pattern_id', models.AutoField(primary_key=True, serialize=False)),
                ('symbol_type_combination', models.CharField(help_text="e.g., 'diameter+tolerance', 'dimension+note'", max_length=100)),
                ('merge_probability', models.FloatField(help_text='% of time users merge this combination')),
                ('spatial_threshold', models.FloatField(help_text='Average distance where users merge vs separate')),
                ('confidence_threshold', models.FloatField(help_text='Minimum confidence users typically accept')),
                ('sample_size', models.IntegerField(help_text='Number of decisions this pattern is based on')),
                ('accuracy_score', models.FloatField(help_text='How reliable this pattern is (0-1)')),
                ('common_rejection_reasons', models.JSONField(help_text='Most frequent rejection patterns')),
                ('typical_merge_distance', models.FloatField(help_text='Typical distance for merging in this template')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.template')),
            ],
            options={
                'db_table': 'template_decision_patterns',
                'indexes': [models.Index(fields=['template', 'merge_probability'], name='template_de_templat_0e2af1_idx'), models.Index(fields=['accuracy_score'], name='template_de_accurac_644795_idx')],
                'unique_together': {('template', 'symbol_type_combination')},
            },
        ),
        migrations.AddIndex(
            model_name='userdecisionrecord',
            index=models.Index(fields=['template', 'decision_type'], name='user_decisi_templat_80a7ca_idx'),
        ),
        migrations.AddIndex(
            model_name='userdecisionrecord',
            index=models.Index(fields=['company', 'cage_type'], name='user_decisi_company_03d8e3_idx'),
        ),
        migrations.AddIndex(
            model_name='userdecisionrecord',
            index=models.Index(fields=['timestamp'], name='user_decisi_timesta_aa0ced_idx'),
        ),
        migrations.AddIndex(
            model_name='userlearningprofile',
            index=models.Index(fields=['user', 'expertise_level'], name='user_learni_user_id_41286b_idx'),
        ),
        migrations.AddIndex(
            model_name='userlearningprofile',
            index=models.Index(fields=['template', 'decision_consistency_score'], name='user_learni_templat_7d215e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userlearningprofile',
            unique_together={('user', 'template')},
        ),
    ]
