"""
Utility functions for integrating GPT-4 Vision with EasyOCR for enhanced symbol detection.
This module provides a hybrid approach that uses EasyOCR for initial text detection
and GPT-4 Vision for more accurate interpretation of complex engineering notations.

The detection windows created by EasyOCR are expanded by 200px (100px on each side)
to provide more context for GPT-4 Vision analysis, improving symbol recognition accuracy.
"""

import os
import base64
import logging
import tempfile
import requests
import json
from PIL import Image, ImageDraw
import numpy as np
from django.conf import settings
from .ocr_enhancement import merge_nearby_detections, apply_pattern_recognition

logger = logging.getLogger(__name__)

# Configuration for GPT-4 Vision
GPT_VISION_API_KEY = settings.AZURE_OPENAI_API_KEY
GPT_VISION_ENDPOINT = settings.AZURE_OPENAI_ENDPOINT
GPT_VISION_MODEL = settings.AZURE_OPENAI_MODEL_NAME

def encode_image_to_base64(image_path):
    """
    Encode an image to base64 for sending to GPT-4 Vision API.

    Args:
        image_path: Path to the image file

    Returns:
        string: Base64-encoded image
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def create_crop_around_text(image, bbox, padding=100):
    """
    Create a crop around detected text with padding.

    Args:
        image: PIL Image object
        bbox: Bounding box of detected text [[x1,y1],[x2,y1],[x2,y2],[x1,y2]]
        padding: Padding to add around the bounding box (pixels)
                Default is now 100px to create a window 200px larger (100px on each side)

    Returns:
        tuple: (cropped_image, crop_coordinates, is_vertical)
    """
    # Calculate crop boundaries with padding
    min_x = max(0, min(bbox[0][0], bbox[3][0]) - padding)
    min_y = max(0, min(bbox[0][1], bbox[1][1]) - padding)
    max_x = min(image.width, max(bbox[1][0], bbox[2][0]) + padding)
    max_y = min(image.height, max(bbox[2][1], bbox[3][1]) + padding)

    # Create crop
    crop = image.crop((min_x, min_y, max_x, max_y))

    # Determine if text is vertical
    crop_width = max_x - min_x
    crop_height = max_y - min_y
    is_vertical = crop_height > crop_width * 1.5  # Using 1.5 as a threshold to identify clearly vertical text

    # Return crop, its coordinates in the original image, and whether it's vertical
    return crop, (min_x, min_y, max_x, max_y), is_vertical

def analyze_with_gpt_vision(image_path, prompt):
    """
    Analyze an image with GPT-4 Vision API.

    Args:
        image_path: Path to the image file
        prompt: Prompt to send to GPT-4 Vision

    Returns:
        string: GPT-4 Vision response
    """
    try:
        # Encode image to base64
        base64_image = encode_image_to_base64(image_path)

        # Prepare the API request
        headers = {
            "Content-Type": "application/json",
            "api-key": GPT_VISION_API_KEY
        }

        # Construct the messages payload
        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert in engineering drawings and technical measurements. Your task is to accurately interpret measurement values, tolerances, and engineering notations from images."
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 300
        }

        # Make the API request
        response = requests.post(
            f"{GPT_VISION_ENDPOINT}/openai/deployments/{GPT_VISION_MODEL}/chat/completions?api-version=2023-05-15",
            headers=headers,
            json=payload
        )

        # Parse the response
        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        else:
            logger.error(f"GPT-4 Vision API error: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logger.error(f"Error in GPT-4 Vision analysis: {str(e)}")
        return None

def process_image_with_hybrid_approach(segment_image_path, symbols, debug=False, batch_size=5, batch_delay=1.0):
    """
    Process an image using the hybrid approach of EasyOCR and GPT-4 Vision.

    Args:
        segment_image_path: Path to the segment image
        symbols: List of Symbol objects to match against
        debug: Whether to generate debug visualizations
        batch_size: Number of crops to process in a batch before pausing
        batch_delay: Delay in seconds between batches to avoid rate limiting

    Returns:
        tuple: (matched_symbols, debug_image_path)
    """
    import easyocr
    import cv2
    import numpy as np
    import time

    # Initialize EasyOCR reader
    reader = easyocr.Reader(['en'])

    # Validate the image file exists
    if not os.path.exists(segment_image_path):
        logger.error(f"Image file not found: {segment_image_path}")
        raise FileNotFoundError(f"Image file not found: {segment_image_path}")

    try:
        # Open the image with PIL first to validate it
        pil_image = Image.open(segment_image_path)
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        # Save a temporary copy in RGB mode to ensure compatibility with OpenCV
        temp_image_path = segment_image_path + ".temp.jpg"
        pil_image.save(temp_image_path, format='JPEG')

        # Process the image with EasyOCR using the temporary file
        logger.info(f"Processing image with EasyOCR: {temp_image_path}")

        # Try to load with OpenCV first to validate
        cv_image = cv2.imread(temp_image_path)
        if cv_image is None:
            logger.error(f"OpenCV could not load the image: {temp_image_path}")
            raise ValueError("OpenCV could not load the image")

        # Now process with EasyOCR
        results = reader.readtext(temp_image_path)

        # Clean up the temporary file
        try:
            os.remove(temp_image_path)
        except Exception as e:
            logger.warning(f"Could not remove temporary file {temp_image_path}: {e}")

        # Apply proximity-based merging to combine nearby text detections
        logger.info(f"Original OCR detections: {len(results)}")
        merged_results = merge_nearby_detections(results, max_distance=50)
        logger.info(f"After merging nearby detections: {len(merged_results)}")

        # Create a debug image if requested
        debug_image_path = None
        if debug:
            debug_image = pil_image.copy()
            debug_draw = ImageDraw.Draw(debug_image)

    except Exception as e:
        logger.error(f"Error processing image with EasyOCR: {str(e)}")
        logger.exception("Full traceback:")
        raise

    try:
        # Process each merged detection with GPT-4 Vision
        matched_symbols = []
        batch_counter = 0

        for i, detection in enumerate(merged_results):
            bbox, text, score = detection

            # Skip low confidence detections
            if score < 0.3:
                logger.info(f"Skipping low confidence detection {i+1}: '{text}' (score: {score:.2f})")
                continue

            try:
                # Create a crop around the detected text with expanded window (200px larger)
                crop, crop_coords, is_vertical = create_crop_around_text(pil_image, bbox)

                # Log the crop dimensions for debugging
                min_x, min_y, max_x, max_y = crop_coords
                crop_width = max_x - min_x
                crop_height = max_y - min_y
                logger.info(f"Created expanded crop {i+1} for '{text}' with dimensions {crop_width}x{crop_height} pixels")

                # If text is vertical, rotate the crop for better analysis
                if is_vertical:
                    logger.info(f"Detected vertical text for crop {i+1}: '{text}'. Rotating for analysis.")
                    # Rotate 270 degrees counter-clockwise (equivalent to 90 clockwise)
                    crop = crop.rotate(270, expand=True)

                # Save the crop to a temporary file for GPT-4 Vision
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                    crop_path = temp_file.name
                    crop.save(crop_path, format='JPEG')

                # Save a copy of the crop for debugging if requested
                debug_crop_path = None
                if debug:
                    # Create a debug crops directory
                    debug_crops_dir = os.path.join(settings.MEDIA_ROOT, 'debug', 'crops')
                    os.makedirs(debug_crops_dir, exist_ok=True)

                    # Create a unique filename for this crop
                    crop_filename = f"crop_{os.path.basename(segment_image_path)}_{i+1}_{'vertical' if is_vertical else 'horizontal'}.jpg"
                    debug_crop_path = os.path.join(debug_crops_dir, crop_filename)

                    # Save the crop in the orientation it's sent to GPT-4 Vision
                    crop.save(debug_crop_path, format='JPEG')
                    logger.info(f"Saved debug crop to: {debug_crop_path}")

                # Draw the crop area on the debug image
                if debug:
                    min_x, min_y, max_x, max_y = crop_coords
                    debug_draw.rectangle([min_x, min_y, max_x, max_y], outline="red", width=3)
                    orientation_text = "VERTICAL" if is_vertical else "HORIZONTAL"
                    crop_url = f"/media/debug/crops/{os.path.basename(debug_crop_path)}" if debug_crop_path else "N/A"
                    debug_draw.text((min_x, min_y - 15), f"Crop {i+1}: {text} ({orientation_text})", fill="red")
                    # Add smaller text with the crop URL
                    debug_draw.text((min_x, min_y - 30), f"Crop URL: {crop_url}", fill="red")

                # Analyze the crop with GPT-4 Vision
                prompt = f"""
                This is a crop from an engineering drawing that may contain one or more measurement values or technical notations.
                If symbols or values are in single line than they are mostly single values, but if they are in different lines then they can be diferent values.
                Identify and interpret ALL measurement values, tolerances, or engineering notations present in this image.
                Return each value on a separate line, properly formatted as it would appear in technical documentation.
                - ALWAYS INCLUDE THE FULL VALUE WITH TOLERANCES in a single symbol.
                - When you see a dimension with a tolerance stacked above or below it (like 0.30 with +0.100 above it), COMBINE them as ONE value (e.g., "0.30 +0.100")
                - If you see a value like "Ø1.95" and nearby "±0.05", combine them as "Ø1.95 ±0.05"
                Examples: "34.66±0.15", "Ø25.4", "45°", "|360°|", "R0.2", "1.55±0.1", etc.
                If multiple values are present, list each one on its own line.
                Do not include any explanations or additional text - only the measurement values.
                """

                gpt_result = analyze_with_gpt_vision(crop_path, prompt)

                # Clean up the temporary file
                try:
                    os.unlink(crop_path)
                except Exception as e:
                    logger.warning(f"Could not remove temporary crop file {crop_path}: {e}")

                # Increment batch counter and add delay if needed
                batch_counter += 1
                if batch_counter >= batch_size:
                    logger.info(f"Processed {batch_counter} crops, pausing for {batch_delay} seconds to avoid rate limiting")
                    time.sleep(batch_delay)
                    batch_counter = 0

                if gpt_result:
                    # Log the GPT-4 Vision result
                    logger.info(f"GPT-4 Vision result for crop {i+1}: '{gpt_result}' (Original OCR: '{text}')")

                    # Split the result by newlines to handle multiple values
                    gpt_values = [value.strip() for value in gpt_result.split('\n') if value.strip()]

                    # Add the GPT result to the debug image
                    if debug:
                        debug_draw.text((min_x, max_y + 5), f"GPT: {', '.join(gpt_values)}", fill="blue")

                    # Calculate center point of the bounding box
                    center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                    center_y = int((bbox[0][1] + bbox[2][1]) / 2)
                    position = (center_x, center_y)

                    # Import the enhanced symbol matching function
                    from api.utils.ocr_enhancement import find_best_symbol_match

                    # Keep track of already matched symbols
                    already_matched_symbols = set([match['symbol'].id for match in matched_symbols])

                    # Process each detected value
                    for j, gpt_value in enumerate(gpt_values):
                        if not gpt_value:
                            continue

                        logger.info(f"Processing GPT value {j+1}/{len(gpt_values)} from crop {i+1}: '{gpt_value}'")

                        # Find the best matching symbol, considering position and already matched symbols
                        # Use a higher similarity threshold for GPT Vision to be more selective
                        best_match, best_similarity = find_best_symbol_match(
                            gpt_value,
                            symbols,
                            similarity_threshold=0.7,
                            position=position,
                            already_matched_symbols=already_matched_symbols
                        )

                        # If a good match is found
                        if best_match:
                            # Add to matched symbols
                            matched_symbols.append({
                                'symbol': best_match,
                                'position': (center_x, center_y),
                                'similarity': best_similarity,
                                'gpt_value': gpt_value,
                                'ocr_value': text,
                                'is_vertical': is_vertical,
                                'crop_number': i + 1  # Track which crop this match came from
                            })

                            # Update already matched symbols to avoid duplicates
                            already_matched_symbols.add(best_match.id)

                            # Add the match to the debug image
                            if debug:
                                debug_draw.text((min_x, max_y + 20 + (j * 15)), f"Match {j+1}: {best_match.value} ({best_similarity:.2f})", fill="green")
                        else:
                            logger.info(f"No match found for GPT value '{gpt_value}' from crop {i+1}")
                            if debug:
                                debug_draw.text((min_x, max_y + 20 + (j * 15)), f"No match: {gpt_value}", fill="orange")
                else:
                    logger.warning(f"No result from GPT-4 Vision for crop {i+1}")
                    if debug:
                        debug_draw.text((min_x, max_y + 5), "GPT: No result", fill="red")

            except Exception as e:
                logger.error(f"Error processing crop {i+1}: {str(e)}")
                # Continue with the next detection instead of failing the whole process
                continue

    except Exception as e:
        logger.error(f"Error in GPT-4 Vision processing: {str(e)}")
        logger.exception("Full traceback:")

        # If we have a debug image, add error message to it
        if debug and 'debug_image' in locals():
            debug_draw.text((10, 10), f"ERROR: {str(e)}", fill="red")

            # Save the debug image even if there was an error
            debug_dir = os.path.join(settings.MEDIA_ROOT, 'debug')
            os.makedirs(debug_dir, exist_ok=True)
            debug_image_path = os.path.join(debug_dir, f"error_gpt_vision_debug_{os.path.basename(segment_image_path)}")
            debug_image.save(debug_image_path)
            logger.info(f"Error debug image saved to: {debug_image_path}")

            return [], debug_image_path

        # Re-raise the exception
        raise

    # Save the debug image if requested
    if debug:
        # Create a debug directory in media root if it doesn't exist
        debug_dir = os.path.join(settings.MEDIA_ROOT, 'debug')
        os.makedirs(debug_dir, exist_ok=True)

        # Create a unique filename for the debug image
        debug_image_path = os.path.join(debug_dir, f"gpt_vision_debug_{os.path.basename(segment_image_path)}")

        # Save the debug image
        debug_image.save(debug_image_path)
        logger.info(f"Debug image saved to: {debug_image_path}")

    return matched_symbols, debug_image_path
