"""
Utility to overlay bubbles on an existing image without redrawing the entire image.
"""

import os
import sys
import django
import traceback
import time
from PIL import Image, ImageDraw, ImageFont
import io

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from django.conf import settings
from api.models import Drawing, Segment, Symbol
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
def overlay_bubbles(request, drawing_id):
    """
    Overlay bubbles on an existing image without redrawing the entire image.
    This function:
    1. Gets the original drawing image
    2. Gets all symbol positions from the database
    3. Draws bubbles at those positions with the specified size
    4. Returns the image as an HTTP response
    """
    try:
        # Parse the request to get the bubble size
        if request.method == 'POST':
            try:
                import json
                data = json.loads(request.body)
                bubble_size = int(data.get('bubble_size', 60))
            except (json.JSONDecodeError, ValueError):
                bubble_size = 60
        else:
            bubble_size = int(request.GET.get('bubble_size', 60))

        # Check if this is a download request
        is_download = request.GET.get('download', 'false').lower() == 'true'

        print(f"Overlaying bubbles for drawing {drawing_id} with size {bubble_size}, download={is_download}")

        # Get the drawing
        drawing = Drawing.objects.get(id=drawing_id)
        print(f"Found drawing: {drawing}")

        # Get the original image path
        image_path = drawing.image.path
        print(f"Original image path: {image_path}")

        # Check if the original image exists
        if not os.path.exists(image_path):
            print(f"ERROR: Original image file does not exist at {image_path}")
            return HttpResponse("Image not found", status=404)

        # Open the image with PIL
        img = Image.open(image_path)
        print(f"Opened image: {img.size}, mode: {img.mode}")

        # Convert to RGB mode if needed
        if img.mode != 'RGB':
            print(f"Converting image from {img.mode} to RGB")
            img = img.convert('RGB')

        draw = ImageDraw.Draw(img)

        # Calculate font size based on bubble size
        font_size = int(bubble_size * 0.7)  # Font size proportional to bubble size
        print(f"Using font size: {font_size}")

        # Try to load a font, fall back to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
            print("Using Arial font")
        except IOError:
            try:
                # Try system fonts
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
                print("Using Helvetica font")
            except IOError:
                font = ImageFont.load_default()
                print("Using default font")

        # Get all segments for this drawing
        segments = Segment.objects.filter(drawing=drawing)
        print(f"Found {segments.count()} segments")

        # Debug: Print all segments
        for segment in segments:
            print(f"Segment {segment.segment_number}: offset=({segment.segment_x_offset}, {segment.segment_y_offset})")

        # Define the segment order (2, 1, 3, 4, ...)
        segment_order = []

        # First, try to find segments with numbers 2, 1, 3, 4
        for segment_number in [2, 1, 3, 4]:
            segment = segments.filter(segment_number=segment_number).first()
            if segment:
                segment_order.append(segment)
                print(f"Added segment {segment_number} to order")

        # Add any remaining segments in their natural order
        for segment in segments:
            if segment not in segment_order:
                segment_order.append(segment)
                print(f"Added additional segment {segment.segment_number} to order")

        # Initialize a counter for symbol numbering across all segments
        symbol_counter = 1
        total_symbols = 0

        # Process each segment in the specified order (2, 1, 3, 4)
        for segment in segment_order:
            # Get all marked symbols for this segment
            symbols = Symbol.objects.filter(segment=segment, is_marked=True).order_by('display_order', 'id')
            print(f"Segment {segment.segment_number}: Found {symbols.count()} marked symbols")
            total_symbols += symbols.count()

            # Calculate the offset for this segment
            x_offset = segment.segment_x_offset or 0
            y_offset = segment.segment_y_offset or 0
            print(f"Segment {segment.segment_number} offset: ({x_offset}, {y_offset})")

            # Draw each symbol with its sequential number
            for symbol in symbols:
                # Calculate the absolute position in the full drawing
                abs_x = x_offset + symbol.x_coordinate
                abs_y = y_offset + symbol.y_coordinate
                print(f"Symbol {symbol_counter}: type={symbol.symbol_type}, value={symbol.value}, pos=({abs_x}, {abs_y}), original_pos=({symbol.x_coordinate}, {symbol.y_coordinate})")

                # Draw a circle for the symbol - using the specified bubble size
                circle_radius = int(bubble_size / 2)  # Radius is half the diameter
                print(f"Using circle radius: {circle_radius}")

                # Draw outer circle in blue
                draw.ellipse(
                    [(abs_x - circle_radius, abs_y - circle_radius),
                     (abs_x + circle_radius, abs_y + circle_radius)],
                    fill="blue",
                    outline="blue",
                    width=max(1, int(bubble_size * 0.05))  # Width proportional to bubble size
                )

                # Draw inner circle in blue with white outline for better visibility
                inner_radius = int(circle_radius * 0.9)
                draw.ellipse(
                    [(abs_x - inner_radius, abs_y - inner_radius),
                     (abs_x + inner_radius, abs_y + inner_radius)],
                    fill="blue",
                    outline="white",
                    width=max(1, int(bubble_size * 0.02))  # Width proportional to bubble size
                )

                # Draw the symbol number
                try:
                    # For newer PIL versions
                    left, top, right, bottom = draw.textbbox((0, 0), str(symbol_counter), font=font)
                    text_width = right - left
                    text_height = bottom - top
                    print(f"Text dimensions: {text_width}x{text_height}")
                except AttributeError:
                    # Fallback for older PIL versions
                    try:
                        text_width, text_height = draw.textsize(str(symbol_counter), font=font)
                        print(f"Text dimensions (fallback): {text_width}x{text_height}")
                    except:
                        text_width, text_height = font_size // 2, font_size // 2  # Default fallback values
                        print(f"Text dimensions (default): {text_width}x{text_height}")

                # Draw white text with symbol number
                draw.text(
                    (abs_x - text_width/2, abs_y - text_height/2),
                    str(symbol_counter),
                    fill="white",
                    font=font
                )

                # Increment the counter
                symbol_counter += 1

        print(f"Total symbols drawn: {total_symbols}")

        # Save the image to a BytesIO object
        img_io = io.BytesIO()
        img.save(img_io, format='PNG')
        img_io.seek(0)

        # Return the image as an HTTP response
        response = HttpResponse(img_io, content_type='image/png')
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'

        # If this is a download request, set the Content-Disposition header
        if is_download:
            filename = f"drawing_{drawing_id}_with_bubbles.png"
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            print(f"Serving image as download with filename: {filename}")

        return response

    except Exception as e:
        print(f"ERROR: {e}")
        traceback.print_exc()
        return HttpResponse(f"Error: {str(e)}", status=500)

# For testing directly
if __name__ == "__main__":
    from django.test import RequestFactory

    # Create a test request
    factory = RequestFactory()
    request = factory.get('/overlay-bubbles/47/')

    # Call the view function
    response = overlay_bubbles(request, 47)

    # Print the response status code
    print(f"Response status code: {response.status_code}")
