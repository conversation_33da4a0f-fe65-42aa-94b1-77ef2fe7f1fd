"""
Process B GPT Vision utilities for bounding box processing
"""
import base64
import io
import requests
import asyncio
import aiohttp
import time
from PIL import Image


def process_bounding_box_with_gpt_vision(cropped_image, detected_text=""):
    """
    Process a cropped bounding box image with GPT Vision to identify engineering symbols
    with enhanced spatial analysis and merging recommendations.
    Tries both normal and rotated orientations, and can detect multiple values.

    Args:
        cropped_image: PIL Image object of the cropped bounding box
        detected_text: Text detected by EasyOCR (for context)

    Returns:
        dict: Contains detected values, symbol types, spatial analysis, and recommendations
    """
    # Try both orientations: normal and 90-degree clockwise rotation
    orientations = [
        ("normal", cropped_image),
        ("rotated_90", cropped_image.rotate(-90, expand=True))
    ]

    best_result = None
    best_confidence = 0

    for i, (orientation_name, image) in enumerate(orientations):
        print(f"🔄 Trying {orientation_name} orientation...")

        try:
            result = _process_single_orientation_multi_llm(image, detected_text, orientation_name, i)

            # Check if this result is better
            current_confidence = result.get('confidence', 0)
            if current_confidence > best_confidence:
                best_confidence = current_confidence
                best_result = result
                best_result['orientation'] = orientation_name

        except Exception as e:
            print(f"❌ Error processing {orientation_name} orientation: {str(e)}")
            continue

    # Convert cropped image to base64 for frontend display
    buffered = io.BytesIO()
    cropped_image.save(buffered, format="PNG")
    cropped_image_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')

    # Return best result or fallback
    if best_result:
        print(f"✅ Best result from {best_result.get('orientation', 'unknown')} orientation")
        # Add cropped image data to the result
        best_result['cropped_image_base64'] = cropped_image_base64
        return best_result
    else:
        print("❌ All orientations failed, using fallback")
        return {
            'values': [detected_text] if detected_text else ['UNKNOWN'],
            'symbol_types': ['other'],
            'confidence': 0.0,
            'description': 'All orientations failed',
            'orientation': 'failed',
            'raw_response': '',
            'cropped_image_base64': cropped_image_base64
        }


# Multiple LLM endpoints for load balancing and speed
LLM_ENDPOINTS = [
    {
        'name': 'GPT-4o-Primary',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACHYHv6XJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    {
        'name': 'GPT-4.1-Secondary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BCACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1'
    },
    {
        'name': 'GPT-4o-Secondary',
        'endpoint': 'https://ai-aihub2573706963054.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACYeBjFXJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    {
        'name': 'GPT-4.1-Tertiary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1-2/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BBACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1'
    }
]

def _process_single_orientation_multi_llm(image, detected_text, orientation_name, llm_index=0):
    """
    Process a single orientation using multiple LLM endpoints for load balancing

    Args:
        image: PIL Image object
        detected_text: Text detected by EasyOCR
        orientation_name: Name of the orientation being processed
        llm_index: Index of LLM endpoint to use (for load balancing)

    Returns:
        dict: Processing result for this orientation
    """
    try:
        # Convert PIL image to base64
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')

        # Enhanced prompt for Process B with spatial analysis and merging recommendations
        prompt = f"""You are analyzing a cropped section of a technical engineering drawing. This image may contain ONE OR MORE engineering symbols or measurement values.

EasyOCR detected text: "{detected_text}"
Image orientation: {orientation_name}

CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no markdown, no additional text.

If you find MULTIPLE values in this image, list them all. If you find only ONE value, still use the array format.

Analyze the image and return this exact JSON format:
{{
    "values": ["value1", "value2", "etc"],
    "symbol_types": ["type1", "type2", "etc"],
    "description": "brief description of what you see",
    "count": 2,
    "spatial_analysis": {{
        "symbols_on_same_line": true,
        "horizontal_alignment": "well_aligned",
        "vertical_alignment": "scattered",
        "symbol_grouping": "single_measurement",
        "recommended_action": "merge_all",
        "reasoning": "Diameter symbol with tolerance on same horizontal line, typical engineering notation"
    }}
}}

Symbol types can be: diameter, radius, tolerance, degree, boxed, area, cross-section, dimension, surface_roughness, thread, material, text, other

Spatial Analysis Guidelines:
- symbols_on_same_line: true if symbols are horizontally aligned, false if on different lines
- horizontal_alignment: "well_aligned", "moderately_aligned", or "poorly_aligned"
- vertical_alignment: "aligned", "moderately_aligned", or "scattered"
- symbol_grouping: "single_measurement", "multiple_measurements", or "unrelated"
- recommended_action: "merge_all", "merge_partial", or "keep_separate"
- reasoning: Brief explanation for your recommendation

Merging Logic:
- merge_all: All symbols represent one measurement (e.g., Ø25±0.05)
- merge_partial: Some symbols should be merged, others separate
- keep_separate: Each symbol is independent

Engineering Conventions:
- Diameter (Ø) + tolerance (±) = single measurement → merge_all
- Multiple dimensions on same line = usually separate → keep_separate
- Symbols on different lines = usually separate → keep_separate
- Subscripts/superscripts = part of main symbol → merge_all

Focus on:
1. Engineering measurements (dimensions, tolerances, radii, diameters like Ø31±0.15)
2. Surface roughness symbols (Ra, Rz, etc.)
3. Geometric tolerances and symbols
4. Thread specifications
5. Material callouts
6. Any boxed or circled values
7. MULTIPLE values in the same bounding box (like two diameter measurements stacked vertically)

RESPOND WITH ONLY THE JSON OBJECT - NO OTHER TEXT."""

        # Select LLM endpoint using round-robin load balancing
        llm_config = LLM_ENDPOINTS[llm_index % len(LLM_ENDPOINTS)]
        print(f"🤖 Using {llm_config['name']} for {orientation_name} orientation")

        # API call with selected endpoint
        headers = {
            'Content-Type': 'application/json',
            'api-key': llm_config['api_key']
        }

        payload = {
            "model": llm_config['model'],
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{img_base64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 300,
            "temperature": 0.1
        }

        response = requests.post(
            llm_config['endpoint'],
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            response_data = response.json()
            content = response_data['choices'][0]['message']['content']

            # Enhanced logging
            print(f"GPT Vision Response for '{detected_text}' ({orientation_name}):")
            print(f"Raw content: {content}")
            print("-" * 50)

            # Enhanced JSON parsing with multiple fallback strategies for MULTIPLE VALUES
            import json
            import re

            # Strategy 1: Direct JSON parsing
            try:
                result = json.loads(content)
                print(f"✅ Direct JSON parsing successful: {result}")

                # Handle both old single-value format and new multi-value format with spatial analysis
                if 'values' in result and 'symbol_types' in result:
                    # New multi-value format with spatial analysis
                    spatial_analysis = result.get('spatial_analysis', {})
                    return {
                        'values': result.get('values', [detected_text]),
                        'symbol_types': result.get('symbol_types', ['other']),
                        'description': result.get('description', ''),
                        'count': result.get('count', len(result.get('values', []))),
                        'spatial_analysis': {
                            'symbols_on_same_line': spatial_analysis.get('symbols_on_same_line', False),
                            'horizontal_alignment': spatial_analysis.get('horizontal_alignment', 'unknown'),
                            'vertical_alignment': spatial_analysis.get('vertical_alignment', 'unknown'),
                            'symbol_grouping': spatial_analysis.get('symbol_grouping', 'unknown'),
                            'recommended_action': spatial_analysis.get('recommended_action', 'keep_separate'),
                            'reasoning': spatial_analysis.get('reasoning', 'No reasoning provided')
                        },
                        'raw_response': content
                    }
                else:
                    # Old single-value format - convert to multi-value with default spatial analysis
                    return {
                        'values': [result.get('value', detected_text)],
                        'symbol_types': [result.get('symbol_type', 'other')],
                        'description': result.get('description', ''),
                        'count': 1,
                        'spatial_analysis': {
                            'symbols_on_same_line': True,  # Single symbol is always "on same line"
                            'horizontal_alignment': 'well_aligned',
                            'vertical_alignment': 'aligned',
                            'symbol_grouping': 'single_measurement',
                            'recommended_action': 'keep_separate',
                            'reasoning': 'Single symbol detected, no merging needed'
                        },
                        'raw_response': content
                    }

            except json.JSONDecodeError as e:
                print(f"❌ Direct JSON parsing failed: {e}")

                # Strategy 2: Extract JSON from markdown or mixed content
                json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                json_match = re.search(json_pattern, content, re.DOTALL)

                if json_match:
                    try:
                        json_str = json_match.group(1)
                        result = json.loads(json_str)
                        print(f"✅ Markdown JSON extraction successful: {result}")

                        # Handle both formats with spatial analysis
                        if 'values' in result and 'symbol_types' in result:
                            spatial_analysis = result.get('spatial_analysis', {})
                            return {
                                'values': result.get('values', [detected_text]),
                                'symbol_types': result.get('symbol_types', ['other']),
                                'description': result.get('description', ''),
                                'count': result.get('count', len(result.get('values', []))),
                                'spatial_analysis': {
                                    'symbols_on_same_line': spatial_analysis.get('symbols_on_same_line', False),
                                    'horizontal_alignment': spatial_analysis.get('horizontal_alignment', 'unknown'),
                                    'vertical_alignment': spatial_analysis.get('vertical_alignment', 'unknown'),
                                    'symbol_grouping': spatial_analysis.get('symbol_grouping', 'unknown'),
                                    'recommended_action': spatial_analysis.get('recommended_action', 'keep_separate'),
                                    'reasoning': spatial_analysis.get('reasoning', 'No reasoning provided')
                                },
                                'raw_response': content
                            }
                        else:
                            return {
                                'values': [result.get('value', detected_text)],
                                'symbol_types': [result.get('symbol_type', 'other')],
                                'confidence': result.get('confidence', 0.5),
                                'description': result.get('description', ''),
                                'count': 1,
                                'raw_response': content
                            }
                    except json.JSONDecodeError:
                        print("❌ Markdown JSON extraction failed")

                # Strategy 3: Look for JSON object in the content
                json_pattern2 = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
                json_matches = re.findall(json_pattern2, content, re.DOTALL)

                for json_str in json_matches:
                    try:
                        result = json.loads(json_str)
                        if 'values' in result or 'value' in result:  # Validate it's our expected format
                            print(f"✅ Pattern JSON extraction successful: {result}")

                            # Handle both formats
                            if 'values' in result and 'symbol_types' in result:
                                return {
                                    'values': result.get('values', [detected_text]),
                                    'symbol_types': result.get('symbol_types', ['other']),
                                    'confidence': result.get('confidence', 0.5),
                                    'description': result.get('description', ''),
                                    'count': result.get('count', len(result.get('values', []))),
                                    'raw_response': content
                                }
                            else:
                                return {
                                    'values': [result.get('value', detected_text)],
                                    'symbol_types': [result.get('symbol_type', 'other')],
                                    'confidence': result.get('confidence', 0.5),
                                    'description': result.get('description', ''),
                                    'count': 1,
                                    'raw_response': content
                                }
                    except json.JSONDecodeError:
                        continue

                print("❌ All JSON parsing strategies failed")

                # Strategy 4: Manual parsing fallback for multiple values
                try:
                    # Try to extract values manually from common patterns
                    values_match = re.search(r'"values":\s*\[(.*?)\]', content)
                    types_match = re.search(r'"symbol_types":\s*\[(.*?)\]', content)
                    conf_match = re.search(r'"confidence":\s*([0-9.]+)', content)
                    desc_match = re.search(r'"description":\s*"([^"]*)"', content)

                    if values_match:
                        # Parse array values
                        values_str = values_match.group(1)
                        values = [v.strip().strip('"') for v in values_str.split(',')]

                        types_str = types_match.group(1) if types_match else ''
                        types = [t.strip().strip('"') for t in types_str.split(',')] if types_str else ['other'] * len(values)

                        print(f"✅ Manual multi-value parsing successful")
                        return {
                            'values': values,
                            'symbol_types': types,
                            'confidence': float(conf_match.group(1)) if conf_match else 0.5,
                            'description': desc_match.group(1) if desc_match else 'Manually parsed',
                            'count': len(values),
                            'raw_response': content
                        }
                    else:
                        # Try single value fallback
                        value_match = re.search(r'"value":\s*"([^"]*)"', content)
                        type_match = re.search(r'"symbol_type":\s*"([^"]*)"', content)

                        if value_match:
                            print(f"✅ Manual single-value parsing successful")
                            return {
                                'values': [value_match.group(1)],
                                'symbol_types': [type_match.group(1) if type_match else 'other'],
                                'confidence': float(conf_match.group(1)) if conf_match else 0.5,
                                'description': desc_match.group(1) if desc_match else 'Manually parsed',
                                'count': 1,
                                'raw_response': content
                            }
                except Exception as manual_error:
                    print(f"❌ Manual parsing failed: {manual_error}")

                # Final fallback
                print("❌ Using final fallback")
                return {
                    'values': [detected_text] if detected_text else ['UNKNOWN'],
                    'symbol_types': ['other'],
                    'confidence': 0.3,
                    'description': 'Failed to parse GPT response',
                    'count': 1,
                    'raw_response': content
                }
        else:
            print(f"GPT Vision API error: {response.status_code} - {response.text}")

            # Parse error details for better feedback
            error_message = f'API error: {response.status_code}'
            try:
                error_data = response.json()
                if 'error' in error_data:
                    error_code = error_data['error'].get('code', 'unknown')
                    error_msg = error_data['error'].get('message', 'unknown')

                    if error_code == 'unavailable_model':
                        error_message = 'GPT Vision model unavailable - using EasyOCR fallback'
                    elif error_code == 'RateLimitReached':
                        error_message = 'Rate limit exceeded - please wait before retrying'
                    else:
                        error_message = f'{error_code}: {error_msg}'
            except:
                pass

            return {
                'value': detected_text,
                'symbol_type': 'other',
                'confidence': 0.1,
                'description': error_message,
                'raw_response': response.text,
                'fallback_used': True
            }

    except Exception as e:
        print(f"Error in process_bounding_box_with_gpt_vision: {str(e)}")
        return {
            'value': detected_text,
            'symbol_type': 'other',
            'confidence': 0.0,
            'description': f'Processing error: {str(e)}',
            'raw_response': ''
        }


def is_small_engineering_value_exception(text):
    """
    Check if the detected text is a small engineering value that should be kept even in margin areas.
    This specifically targets values like "0.15", "0.05", etc. that are commonly found in corners.

    Args:
        text: Detected text string

    Returns:
        bool: True if this is a small engineering value that should be preserved
    """
    import re

    # Clean the text
    clean_text = text.strip()

    # Pattern for small decimal values (0.xx format)
    small_decimal_patterns = [
        r'^0\.\d{1,3}$',          # 0.15, 0.05, 0.125, etc.
        r'^±0\.\d{1,3}$',         # ±0.15, ±0.05, etc.
        r'^\+0\.\d{1,3}$',        # +0.15, +0.05, etc.
        r'^-0\.\d{1,3}$',         # -0.15, -0.05, etc.
        r'^0\.\d{1,3}[A-Z]?$',    # 0.15K, 0.05A, etc.
    ]

    for pattern in small_decimal_patterns:
        if re.match(pattern, clean_text):
            print(f"🎯 Small engineering value detected: '{clean_text}' - will preserve despite margin location")
            return True

    # Also check for other small engineering values
    small_value_patterns = [
        r'^[1-9]\.\d{1,2}$',      # 1.5, 2.25, etc. (small single-digit decimals)
        r'^R0\.\d{1,3}$',         # R0.15, R0.05, etc. (small radius values)
        r'^C0\.\d{1,3}$',         # C0.15, C0.05, etc. (small chamfer values)
    ]

    for pattern in small_value_patterns:
        if re.match(pattern, clean_text):
            print(f"🎯 Small engineering value detected: '{clean_text}' - will preserve despite margin location")
            return True

    return False


def filter_segment_margins(bounding_boxes, image_width, image_height, segment_number, custom_margins=None):
    """
    Filter out bounding boxes in segment-specific margin areas

    Args:
        bounding_boxes: List of bounding box dictionaries
        image_width: Width of the segment image
        image_height: Height of the segment image
        segment_number: Segment number (1, 2, 3, or 4)
        custom_margins: Optional dict with custom margin percentages (e.g., {'top': 20, 'left': 10})

    Returns:
        List of filtered bounding boxes (margin boxes removed)
    """
    if not bounding_boxes:
        return []

    # Default 10% margin threshold (reduced from 15% to be less aggressive)
    default_margin_percent = 10

    # Use custom margins if provided, otherwise use defaults
    if custom_margins:
        print(f"🎯 Using custom margins: {custom_margins}")
        top_percent = custom_margins.get('top', default_margin_percent)
        bottom_percent = custom_margins.get('bottom', default_margin_percent)
        left_percent = custom_margins.get('left', default_margin_percent)
        right_percent = custom_margins.get('right', default_margin_percent)
    else:
        print(f"📏 Using default {default_margin_percent}% margins")
        top_percent = bottom_percent = left_percent = right_percent = default_margin_percent

    # Calculate margin boundaries
    left_margin = image_width * (left_percent / 100)
    right_margin = image_width * ((100 - right_percent) / 100)
    top_margin = image_height * (top_percent / 100)
    bottom_margin = image_height * ((100 - bottom_percent) / 100)

    print(f"🔍 Filtering Segment {segment_number} margins (custom: {custom_margins is not None}):")
    print(f"   Image size: {image_width}x{image_height}")
    print(f"   Margins: left={left_margin:.0f}px ({left_percent}%), right={right_margin:.0f}px ({right_percent}%), top={top_margin:.0f}px ({top_percent}%), bottom={bottom_margin:.0f}px ({bottom_percent}%)")

    # Define which margins to filter for each segment
    segment_margin_rules = {
        1: ['top', 'left'],      # Segment 1: filter top + left margins
        2: ['top', 'right'],     # Segment 2: filter top + right margins
        3: ['left', 'bottom'],   # Segment 3: filter left + bottom margins
        4: ['bottom', 'right']   # Segment 4: filter bottom + right margins
    }

    # Get the margin rules for this segment
    margins_to_filter = segment_margin_rules.get(segment_number, [])
    print(f"   Filtering margins: {margins_to_filter}")

    filtered_boxes = []
    margin_boxes_count = 0

    for box in bounding_boxes:
        box_center_x = box['center_x']
        box_center_y = box['center_y']

        # Check if box is in any of the margins to filter
        in_filtered_margin = False

        # Special exception for small engineering values (like "0.15") - don't filter these even in margins
        is_small_engineering_value = is_small_engineering_value_exception(box['detected_text'])

        if is_small_engineering_value:
            print(f"   🎯 Small engineering value exception: '{box['detected_text']}' - keeping despite margin location")
        else:
            for margin in margins_to_filter:
                if margin == 'top' and box_center_y < top_margin:
                    in_filtered_margin = True
                    print(f"   🚫 Filtered (top margin): '{box['detected_text']}' at ({box_center_x}, {box_center_y}) < {top_margin:.0f}")
                    break
                elif margin == 'bottom' and box_center_y > bottom_margin:
                    in_filtered_margin = True
                    print(f"   🚫 Filtered (bottom margin): '{box['detected_text']}' at ({box_center_x}, {box_center_y}) > {bottom_margin:.0f}")
                    break
                elif margin == 'left' and box_center_x < left_margin:
                    in_filtered_margin = True
                    print(f"   🚫 Filtered (left margin): '{box['detected_text']}' at ({box_center_x}, {box_center_y}) < {left_margin:.0f}")
                    break
                elif margin == 'right' and box_center_x > right_margin:
                    in_filtered_margin = True
                    print(f"   🚫 Filtered (right margin): '{box['detected_text']}' at ({box_center_x}, {box_center_y}) > {right_margin:.0f}")
                    break

        if not in_filtered_margin:
            # Additional content-based filtering for extra safety
            if is_likely_engineering_symbol(box['detected_text']):
                filtered_boxes.append(box)
                print(f"   ✅ Kept: '{box['detected_text']}' at ({box_center_x}, {box_center_y})")
            else:
                margin_boxes_count += 1
                print(f"   🚫 Filtered (non-engineering): '{box['detected_text']}' at ({box_center_x}, {box_center_y})")
        else:
            margin_boxes_count += 1

    print(f"📊 Segment {segment_number} filtering results: {len(filtered_boxes)} kept, {margin_boxes_count} filtered out")
    return filtered_boxes


def is_likely_engineering_symbol(text):
    """
    Enhanced check if detected text looks like an engineering symbol
    """
    import re

    # Remove whitespace for analysis
    clean_text = text.strip()

    # Too long = probably not an engineering symbol
    if len(clean_text) > 30:
        return False

    # Enhanced engineering patterns to catch more symbols including longer values
    engineering_patterns = [
        r'Ø\d+[\.\d]*',           # Diameter: Ø31, Ø26.5, Ø0.14
        r'ø\d+[\.\d]*',           # Lowercase diameter: ø31
        r'R\d+[\.\d]*',           # Radius: R12.5, R8
        r'r\d+[\.\d]*',           # Lowercase radius: r8
        r'±\d+[\.\d]*',           # Tolerance: ±0.15, ±0.05
        r'\+\d+[\.\d]*',          # Plus tolerance: +0.15
        r'-\d+[\.\d]*',           # Minus tolerance: -0.05
        r'\d+°',                  # Degrees: 45°, 90°, 360°
        r'M\d+x[\d\.]*',          # Thread: M8x1.25, M12x1.5
        r'm\d+x[\d\.]*',          # Lowercase thread: m8x1.25
        r'\d+\.\d+',              # Decimal numbers: 31.5, 0.15
        r'^\d+$',                 # Simple numbers: 31, 26
        r'[A-Z]-[A-Z]',           # Section markers: A-A, B-B
        r'SR\d+[\.\d]*',          # Surface roughness: SR0.8
        r'Ra\d+[\.\d]*',          # Surface roughness: Ra3.2
        r'Rz\d+[\.\d]*',          # Surface roughness: Rz12.5
        r'\d+\+\d+[\.\d]*',       # Plus dimensions: 34+0.1
        r'\d+-\d+[\.\d]*',        # Minus dimensions: 34-0.1
        r'\(\S+\)',               # Parenthetical values: (31), (S6.74.7)
        r'\[\S+\]',               # Bracketed values: [31]
        r'\d+x\d+[\.\d]*',        # Dimensions: 31x26, 8x7.05
        r'C\d+[\.\d]*',           # Chamfer: C2, C0.5
        r'c\d+[\.\d]*',           # Lowercase chamfer: c2
        r'S\d+[\.\d]*',           # S values: S6.74.7
        r'[A-Z]\d+[\.\d]*',       # Letter-number combinations: A1, B2
        r'\d+[\.\d]*[A-Z]',       # Number-letter combinations: 7.05A
        r'^\d+[\.\d]*$',          # Pure numbers with decimals
        r'^[A-Z]$',               # Single letters: A, B, C, D
    ]

    for pattern in engineering_patterns:
        if re.search(pattern, clean_text):
            return True

    # Enhanced character matching - more permissive for engineering symbols
    if re.match(r'^[\d.,±°Ø\-\+\s\*RMxSRazcC\(\)\[\]]+$', clean_text):
        return True

    # Accept short alphanumeric combinations that could be engineering codes
    if len(clean_text) <= 8 and re.match(r'^[A-Za-z0-9\-\+\.]+$', clean_text):
        return True

    # Reject common margin text (more comprehensive list)
    margin_keywords = [
        'DRAWING', 'REVISION', 'SCALE', 'DATE', 'APPROVED',
        'CHECKED', 'DRAWN', 'SHEET', 'DWG', 'REV', 'TITLE',
        'MATERIAL', 'FINISH', 'TOLERANCE', 'UNLESS', 'OTHERWISE',
        'SPECIFIED', 'DIMENSIONS', 'MILLIMETERS', 'INCHES',
        'NOTES', 'GENERAL', 'STANDARD', 'PART', 'NUMBER',
        'ASSEMBLY', 'DETAIL', 'VIEW', 'SECTION', 'PROJECTION',
        'THIRD', 'ANGLE', 'FIRST', 'ISO', 'ANSI', 'DIN'
    ]

    for keyword in margin_keywords:
        if keyword.upper() in clean_text.upper():
            print(f"🚫 Rejecting margin keyword: '{clean_text}' (contains '{keyword}')")
            return False

    # If we get here, it's likely an engineering symbol
    print(f"✅ Accepting as engineering symbol: '{clean_text}'")
    return True


def merge_overlapping_bounding_boxes(bounding_boxes, merge_threshold=30):
    """
    Merge overlapping or nearby bounding boxes to avoid duplicate processing

    Args:
        bounding_boxes: List of bounding box dictionaries
        merge_threshold: Distance threshold for merging boxes (pixels)

    Returns:
        List of merged bounding box dictionaries
    """
    if not bounding_boxes:
        return []

    print(f"🔄 Merging {len(bounding_boxes)} bounding boxes with threshold {merge_threshold}px")

    merged = []
    used = set()

    for i, box1 in enumerate(bounding_boxes):
        if i in used:
            continue

        # Start a new merged box
        merged_box = {
            'x': box1['x'],
            'y': box1['y'],
            'width': box1['width'],
            'height': box1['height'],
            'center_x': box1['center_x'],
            'center_y': box1['center_y'],
            'detected_text': box1['detected_text'],
            'confidence': box1['confidence'],
            'merged_from': [box1['detected_text']]  # Track what was merged
        }
        used.add(i)

        # Find overlapping/nearby boxes to merge
        for j, box2 in enumerate(bounding_boxes):
            if j in used or i == j:
                continue

            # Check if boxes should be merged
            if should_merge_boxes(merged_box, box2, merge_threshold):
                print(f"  📦 Merging '{merged_box['detected_text']}' with '{box2['detected_text']}'")

                # Merge the boxes by expanding the bounding area
                min_x = min(merged_box['x'], box2['x'])
                min_y = min(merged_box['y'], box2['y'])
                max_x = max(merged_box['x'] + merged_box['width'], box2['x'] + box2['width'])
                max_y = max(merged_box['y'] + merged_box['height'], box2['y'] + box2['height'])

                merged_box['x'] = min_x
                merged_box['y'] = min_y
                merged_box['width'] = max_x - min_x
                merged_box['height'] = max_y - min_y
                merged_box['center_x'] = min_x + merged_box['width'] // 2
                merged_box['center_y'] = min_y + merged_box['height'] // 2

                # Combine detected text
                merged_box['detected_text'] += ' ' + box2['detected_text']
                merged_box['confidence'] = max(merged_box['confidence'], box2['confidence'])
                merged_box['merged_from'].append(box2['detected_text'])

                used.add(j)

        merged.append(merged_box)

    print(f"✅ Merged into {len(merged)} bounding boxes")
    for box in merged:
        if len(box['merged_from']) > 1:
            print(f"  📦 Merged box: '{box['detected_text']}' (from: {box['merged_from']})")

    return merged


def should_merge_boxes(box1, box2, threshold):
    """
    Conservative merging logic to preserve individual symbols while merging only truly overlapping boxes

    Args:
        box1: First bounding box (dict)
        box2: Second bounding box (dict)
        threshold: Distance threshold for merging

    Returns:
        bool: True if boxes should be merged
    """
    x1, y1, w1, h1 = box1['x'], box1['y'], box1['width'], box1['height']
    x2, y2, w2, h2 = box2['x'], box2['y'], box2['width'], box2['height']

    # Calculate overlap
    overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
    overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
    overlap_area = overlap_x * overlap_y

    # Calculate areas
    area1 = w1 * h1
    area2 = w2 * h2

    # CONSERVATIVE: Only merge if there's SIGNIFICANT overlap (30% or more)
    if overlap_area > 0:
        overlap_ratio = overlap_area / min(area1, area2)
        if overlap_ratio > 0.3:  # 30% overlap threshold (much more conservative)
            return True

    # Check if one box is almost completely inside another (nested detection)
    # This handles cases where EasyOCR detects both a word and part of that word
    smaller_area = min(area1, area2)
    larger_area = max(area1, area2)

    if overlap_area > smaller_area * 0.8:  # 80% of smaller box is inside larger box
        return True

    # VERY conservative distance-based merging - only for extremely close boxes
    center1_x, center1_y = x1 + w1//2, y1 + h1//2
    center2_x, center2_y = x2 + w2//2, y2 + h2//2
    distance = ((center1_x - center2_x)**2 + (center1_y - center2_y)**2)**0.5

    # Only merge if boxes are VERY close (much smaller threshold)
    if distance <= threshold * 0.5:  # 0.5x more conservative
        return True

    # Check for exact containment (one box completely inside another)
    tolerance = 5  # Very small tolerance
    if (x1 <= x2 <= x1 + w1 and y1 <= y2 <= y1 + h1 and
        x1 <= x2 + w2 <= x1 + w1 and y1 <= y2 + h2 <= y1 + h1) or \
       (x2 <= x1 <= x2 + w2 and y2 <= y1 <= y2 + h2 and
        x2 <= x1 + w1 <= x2 + w2 and y2 <= y1 + h1 <= y2 + h2):
        return True

    # Do NOT merge based on proximity alone - preserve individual symbols
    return False


def merge_adjacent_bounding_boxes(bounding_boxes, merge_threshold=50):
    """
    Merge adjacent or overlapping bounding boxes

    Args:
        bounding_boxes: List of bounding box objects
        merge_threshold: Distance threshold for merging boxes

    Returns:
        List of merged bounding boxes
    """
    if not bounding_boxes:
        return []

    merged = []
    used = set()

    for i, box1 in enumerate(bounding_boxes):
        if i in used:
            continue

        # Start a new merged box
        merged_box = {
            'x': box1.x,
            'y': box1.y,
            'width': box1.width,
            'height': box1.height,
            'detected_text': box1.detected_text,
            'confidence': box1.confidence,
            'boxes': [box1]
        }
        used.add(i)

        # Find adjacent boxes to merge
        for j, box2 in enumerate(bounding_boxes):
            if j in used or i == j:
                continue

            # Check if boxes are close enough to merge
            if boxes_should_merge(merged_box, box2, merge_threshold):
                # Merge the boxes
                min_x = min(merged_box['x'], box2.x)
                min_y = min(merged_box['y'], box2.y)
                max_x = max(merged_box['x'] + merged_box['width'], box2.x + box2.width)
                max_y = max(merged_box['y'] + merged_box['height'], box2.y + box2.height)

                merged_box['x'] = min_x
                merged_box['y'] = min_y
                merged_box['width'] = max_x - min_x
                merged_box['height'] = max_y - min_y
                merged_box['detected_text'] += ' ' + box2.detected_text
                merged_box['confidence'] = max(merged_box['confidence'], box2.confidence)
                merged_box['boxes'].append(box2)
                used.add(j)

        # Calculate center coordinates
        merged_box['center_x'] = merged_box['x'] + merged_box['width'] // 2
        merged_box['center_y'] = merged_box['y'] + merged_box['height'] // 2

        merged.append(merged_box)

    return merged


async def process_multiple_bounding_boxes_parallel(bounding_boxes, segment_image_path, batch_size=5, delay_between_batches=2):
    """
    Process multiple bounding boxes in parallel batches to optimize speed while respecting rate limits

    Args:
        bounding_boxes: List of BoundingBox objects
        segment_image_path: Path to the segment image
        batch_size: Number of boxes to process in parallel (default: 5)
        delay_between_batches: Delay in seconds between batches (default: 2)

    Returns:
        List of results for each bounding box
    """
    import os
    from PIL import Image

    if not os.path.exists(segment_image_path):
        raise FileNotFoundError(f"Segment image not found: {segment_image_path}")

    # Load the segment image once
    segment_image = Image.open(segment_image_path)
    if segment_image.mode != 'RGB':
        segment_image = segment_image.convert('RGB')

    results = []
    total_boxes = len(bounding_boxes)

    print(f"🚀 Starting parallel processing of {total_boxes} bounding boxes")
    print(f"📦 Batch size: {batch_size}, Delay between batches: {delay_between_batches}s")

    # Process in batches
    for i in range(0, total_boxes, batch_size):
        batch = bounding_boxes[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        total_batches = (total_boxes + batch_size - 1) // batch_size

        print(f"\n🔄 Processing batch {batch_num}/{total_batches} ({len(batch)} boxes)")

        # Add delay between batches (except for the first batch)
        if i > 0:
            print(f"⏳ Waiting {delay_between_batches}s before next batch...")
            await asyncio.sleep(delay_between_batches)

        # Process batch in parallel
        batch_tasks = []
        for box in batch:
            task = process_single_box_async(box, segment_image)
            batch_tasks.append(task)

        # Wait for all tasks in this batch to complete
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

        # Process results and handle exceptions
        for j, result in enumerate(batch_results):
            box = batch[j]
            if isinstance(result, Exception):
                print(f"❌ Error processing box {box.id}: {str(result)}")
                results.append({
                    'box_id': box.id,
                    'success': False,
                    'error': str(result),
                    'gpt_result': {
                        'value': box.detected_text,
                        'symbol_type': 'other',
                        'confidence': 0.0,
                        'description': f'Processing error: {str(result)}',
                        'fallback_used': True
                    }
                })
            else:
                # Extract values for logging
                values = result.get('values', [result.get('value', 'unknown')])
                if isinstance(values, list) and values:
                    value_str = ', '.join(values)
                else:
                    value_str = str(values) if values else 'unknown'

                print(f"✅ Box {box.id} processed successfully: {value_str}")
                results.append({
                    'box_id': box.id,
                    'success': True,
                    'gpt_result': result
                })

    print(f"\n🎉 Parallel processing complete! Processed {len(results)} boxes")
    return results


async def process_single_box_async(bounding_box, segment_image):
    """
    Async wrapper for processing a single bounding box

    Args:
        bounding_box: BoundingBox object
        segment_image: PIL Image of the segment

    Returns:
        dict: GPT Vision result
    """
    try:
        # Crop the image for this bounding box with 15px padding for better LLM analysis
        padding = 15  # 15px padding to avoid reading unnecessary values
        left = max(0, bounding_box.x - padding)
        top = max(0, bounding_box.y - padding)
        right = min(segment_image.width, bounding_box.x + bounding_box.width + padding)
        bottom = min(segment_image.height, bounding_box.y + bounding_box.height + padding)

        crop_box = (left, top, right, bottom)
        cropped_img = segment_image.crop(crop_box)

        # Process with GPT Vision (this will use the synchronous function)
        result = await asyncio.get_event_loop().run_in_executor(
            None,
            process_bounding_box_with_gpt_vision,
            cropped_img,
            bounding_box.detected_text
        )

        return result

    except Exception as e:
        print(f"Error in process_single_box_async for box {bounding_box.id}: {str(e)}")
        raise


def process_multiple_bounding_boxes_sync(bounding_boxes, segment_image_path, batch_size=5, delay_between_batches=2):
    """
    Synchronous wrapper for the async parallel processing function

    Args:
        bounding_boxes: List of BoundingBox objects
        segment_image_path: Path to the segment image
        batch_size: Number of boxes to process in parallel (default: 5)
        delay_between_batches: Delay in seconds between batches (default: 2)

    Returns:
        List of results for each bounding box
    """
    try:
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Run the async function
        results = loop.run_until_complete(
            process_multiple_bounding_boxes_parallel(
                bounding_boxes,
                segment_image_path,
                batch_size,
                delay_between_batches
            )
        )

        return results

    except Exception as e:
        print(f"Error in sync wrapper: {str(e)}")
        raise
    finally:
        # Clean up the event loop
        try:
            loop.close()
        except:
            pass


def boxes_should_merge(box1, box2, threshold):
    """
    Determine if two bounding boxes should be merged

    Args:
        box1: First bounding box (dict or object)
        box2: Second bounding box (object)
        threshold: Distance threshold

    Returns:
        bool: True if boxes should be merged
    """
    # Get coordinates for box1 (could be dict or object)
    if isinstance(box1, dict):
        x1, y1, w1, h1 = box1['x'], box1['y'], box1['width'], box1['height']
    else:
        x1, y1, w1, h1 = box1.x, box1.y, box1.width, box1.height

    # Get coordinates for box2 (object)
    x2, y2, w2, h2 = box2.x, box2.y, box2.width, box2.height

    # Check if boxes overlap or are within threshold distance
    horizontal_distance = max(0, max(x1, x2) - min(x1 + w1, x2 + w2))
    vertical_distance = max(0, max(y1, y2) - min(y1 + h1, y2 + h2))

    return horizontal_distance <= threshold and vertical_distance <= threshold


def process_bounding_box_with_gpt_vision_multi_llm(cropped_image, detected_text="", llm_index=0):
    """
    Enhanced version that uses multiple LLM endpoints for load balancing

    Args:
        cropped_image: PIL Image object of the cropped bounding box
        detected_text: Text detected by EasyOCR (for context)
        llm_index: Index of LLM endpoint to use

    Returns:
        dict: Contains detected values, symbol types, confidence, and cropped image data
    """
    # Try both orientations: normal and 90-degree clockwise rotation
    orientations = [
        ("normal", cropped_image),
        ("rotated_90", cropped_image.rotate(-90, expand=True))
    ]

    best_result = None
    best_confidence = 0

    for i, (orientation_name, image) in enumerate(orientations):
        print(f"🔄 Trying {orientation_name} orientation with LLM {llm_index}...")

        try:
            # Use the specific LLM endpoint for this orientation
            result = _process_single_orientation_multi_llm(image, detected_text, orientation_name, llm_index)

            # Check if this result is better
            current_confidence = result.get('confidence', 0)
            if current_confidence > best_confidence:
                best_confidence = current_confidence
                best_result = result
                best_result['orientation'] = orientation_name
                print(f"✅ Better result from {orientation_name} orientation: confidence {current_confidence}")

        except Exception as e:
            print(f"❌ Error processing {orientation_name} orientation: {str(e)}")
            continue

    # Convert cropped image to base64 for frontend display
    import io
    import base64
    buffered = io.BytesIO()
    cropped_image.save(buffered, format="PNG")
    cropped_image_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')

    # Return best result or fallback
    if best_result:
        print(f"✅ Best result from {best_result.get('orientation', 'unknown')} orientation using LLM {llm_index}")
        # Add cropped image data to the result
        best_result['cropped_image_base64'] = cropped_image_base64
        return best_result
    else:
        print(f"❌ All orientations failed with LLM {llm_index}, using fallback")
        return {
            'values': [detected_text] if detected_text else ['UNKNOWN'],
            'symbol_types': ['other'],
            'confidence': 0.0,
            'description': 'All orientations failed',
            'orientation': 'failed',
            'raw_response': '',
            'cropped_image_base64': cropped_image_base64
        }
