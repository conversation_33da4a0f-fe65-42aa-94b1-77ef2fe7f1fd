"""
Position Fix Utility
This module contains functions to fix symbol positioning issues.
"""

import logging
from django.db import transaction
from api.models import Symbol, Segment

logger = logging.getLogger(__name__)

def fix_symbol_positions(segment_id):
    """
    Fix symbol positions for a given segment by ensuring proper coordinate assignment.
    
    Args:
        segment_id: The ID of the segment to fix
        
    Returns:
        dict: Summary of fixes applied
    """
    try:
        segment = Segment.objects.get(id=segment_id)
        symbols = Symbol.objects.filter(segment=segment)
        
        fixes_applied = {
            'total_symbols': symbols.count(),
            'position_fixes': 0,
            'duplicate_removals': 0,
            'coordinate_updates': 0
        }
        
        with transaction.atomic():
            # Step 1: Remove duplicate symbols with same value and different orientations
            duplicate_groups = {}
            for symbol in symbols:
                key = symbol.value.strip().replace(' ', '')
                if key not in duplicate_groups:
                    duplicate_groups[key] = []
                duplicate_groups[key].append(symbol)
            
            # Remove duplicates (keep one from each group)
            for value, symbol_group in duplicate_groups.items():
                if len(symbol_group) == 2:
                    # Check if they have different orientations
                    orientations = [s.orientation for s in symbol_group]
                    if 'normal' in orientations and 'rotated' in orientations:
                        # Keep the normal orientation, remove the rotated one
                        rotated_symbol = next(s for s in symbol_group if s.orientation == 'rotated')
                        logger.info(f"Removing duplicate rotated symbol: {rotated_symbol.value}")
                        rotated_symbol.delete()
                        fixes_applied['duplicate_removals'] += 1
            
            # Step 2: Fix position sources
            remaining_symbols = Symbol.objects.filter(segment=segment)
            for symbol in remaining_symbols:
                position_updated = False
                
                # Fix position source naming
                if symbol.position_source == 'gpt_vision':
                    # This is correct, no change needed
                    pass
                elif symbol.position_source == 'gpt':
                    # Check if this symbol actually has proper coordinates
                    if symbol.x_coordinate == 100 and symbol.y_coordinate == 100:
                        # This was likely a default position, mark for EasyOCR processing
                        symbol.position_source = 'gpt'
                        symbol.is_marked = False  # Unmark until proper position is found
                        position_updated = True
                
                # Step 3: Validate coordinates
                if symbol.x_coordinate < 0 or symbol.y_coordinate < 0:
                    symbol.x_coordinate = 100
                    symbol.y_coordinate = 100
                    symbol.is_marked = False
                    position_updated = True
                    fixes_applied['coordinate_updates'] += 1
                
                if position_updated:
                    symbol.save()
                    fixes_applied['position_fixes'] += 1
        
        logger.info(f"Position fixes completed for segment {segment_id}: {fixes_applied}")
        return fixes_applied
        
    except Exception as e:
        logger.error(f"Error fixing positions for segment {segment_id}: {str(e)}")
        return {'error': str(e)}

def reorder_symbols_by_position(segment_id):
    """
    Reorder symbols based on their actual positions in the image.
    
    Args:
        segment_id: The ID of the segment to reorder
        
    Returns:
        dict: Summary of reordering
    """
    try:
        segment = Segment.objects.get(id=segment_id)
        symbols = Symbol.objects.filter(segment=segment, is_marked=True).order_by('y_coordinate', 'x_coordinate')
        
        # Update display_order based on position
        for index, symbol in enumerate(symbols, 1):
            symbol.display_order = index
            symbol.save()
        
        logger.info(f"Reordered {symbols.count()} symbols for segment {segment_id}")
        return {'reordered_count': symbols.count()}
        
    except Exception as e:
        logger.error(f"Error reordering symbols for segment {segment_id}: {str(e)}")
        return {'error': str(e)}

def validate_symbol_positions(segment_id):
    """
    Validate that symbol positions make sense within the segment boundaries.
    
    Args:
        segment_id: The ID of the segment to validate
        
    Returns:
        dict: Validation results
    """
    try:
        segment = Segment.objects.get(id=segment_id)
        symbols = Symbol.objects.filter(segment=segment)
        
        # Get segment image dimensions (approximate)
        # In a real implementation, we would get actual image dimensions
        max_width = 800  # Approximate segment width
        max_height = 600  # Approximate segment height
        
        validation_results = {
            'total_symbols': symbols.count(),
            'valid_positions': 0,
            'invalid_positions': 0,
            'out_of_bounds': 0
        }
        
        for symbol in symbols:
            if (0 <= symbol.x_coordinate <= max_width and 
                0 <= symbol.y_coordinate <= max_height):
                validation_results['valid_positions'] += 1
            else:
                validation_results['out_of_bounds'] += 1
                if symbol.x_coordinate < 0 or symbol.y_coordinate < 0:
                    validation_results['invalid_positions'] += 1
        
        return validation_results
        
    except Exception as e:
        logger.error(f"Error validating positions for segment {segment_id}: {str(e)}")
        return {'error': str(e)}

def debug_symbol_positions(segment_id):
    """
    Debug function to print all symbol positions for a segment.
    
    Args:
        segment_id: The ID of the segment to debug
        
    Returns:
        list: List of symbol position data
    """
    try:
        segment = Segment.objects.get(id=segment_id)
        symbols = Symbol.objects.filter(segment=segment).order_by('id')
        
        debug_data = []
        for symbol in symbols:
            debug_data.append({
                'id': symbol.id,
                'value': symbol.value,
                'type': symbol.symbol_type,
                'position': f"({symbol.x_coordinate}, {symbol.y_coordinate})",
                'source': symbol.position_source,
                'orientation': symbol.orientation,
                'is_marked': symbol.is_marked
            })
            
        logger.info(f"Debug data for segment {segment_id}: {debug_data}")
        return debug_data
        
    except Exception as e:
        logger.error(f"Error debugging positions for segment {segment_id}: {str(e)}")
        return {'error': str(e)}
