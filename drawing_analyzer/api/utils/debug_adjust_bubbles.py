"""
Debug script for the adjust_final_bubbles function.
"""

import os
import sys
import django
import json
import traceback

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from django.conf import settings
from rest_framework.test import APIRequestFactory
from api.views import adjust_final_bubbles
from api.models import Drawing, Segment, Symbol

def debug_adjust_bubbles_response(drawing_id, bubble_size=60):
    """
    Debug the response from the adjust_final_bubbles function.
    """
    try:
        # Create a request
        factory = APIRequestFactory()
        request = factory.post(f'/api/adjust-bubbles/{drawing_id}/', {'bubble_size': bubble_size}, format='json')
        
        # Call the view function
        print(f"Calling adjust_final_bubbles with drawing_id={drawing_id}, bubble_size={bubble_size}")
        response = adjust_final_bubbles(request, drawing_id)
        
        # Print the response
        print(f"Response status code: {response.status_code}")
        print(f"Response data: {json.dumps(response.data, indent=2)}")
        
        # Check if the drawing was updated
        drawing = Drawing.objects.get(id=drawing_id)
        print(f"\nDrawing after adjustment:")
        print(f"finalized_image: {drawing.finalized_image}")
        
        # Return the response data
        return response.data
    
    except Exception as e:
        print(f"ERROR: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python debug_adjust_bubbles.py <drawing_id> [bubble_size]")
        sys.exit(1)
    
    drawing_id = int(sys.argv[1])
    bubble_size = int(sys.argv[2]) if len(sys.argv) > 2 else 60
    
    print(f"Debugging adjust_final_bubbles for drawing {drawing_id} with bubble size {bubble_size}")
    data = debug_adjust_bubbles_response(drawing_id, bubble_size)
    
    if data:
        print("\nSuccess! adjust_final_bubbles debug complete.")
    else:
        print("\nFailed to debug adjust_final_bubbles.")
