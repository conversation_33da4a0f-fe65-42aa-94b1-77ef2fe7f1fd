"""
Decision Learning Models for Process B Pattern Recognition
Stores user decisions and spatial analysis for intelligent automation
"""

from django.db import models
from django.contrib.auth.models import User
from .models import Drawing, Segment, Symbol, Template, Company, CageType
import json


class UserDecisionRecord(models.Model):
    """
    Records every user decision for pattern learning
    """
    DECISION_TYPES = [
        ('ACCEPT_SINGLE', 'Accept Single Symbol'),
        ('REJECT_SINGLE', 'Reject Single Symbol'),
        ('MERGE_ALL', 'Merge All Symbols'),
        ('MERGE_PARTIAL', 'Merge Some Symbols'),
        ('REJECT_ALL', 'Reject All Symbols'),
    ]
    
    # Decision metadata
    decision_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Context
    drawing = models.ForeignKey(Drawing, on_delete=models.CASCADE)
    segment = models.ForeignKey(Segment, on_delete=models.CASCADE)
    template = models.ForeignKey(Template, on_delete=models.SET_NULL, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True)
    cage_type = models.ForeignKey(CageType, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Decision details
    decision_type = models.CharField(max_length=20, choices=DECISION_TYPES)
    processing_time = models.FloatField(help_text="Time taken to make decision in seconds")
    user_notes = models.TextField(blank=True, help_text="Optional user comments")
    
    # GPT Vision context
    gpt_recommended_action = models.CharField(max_length=20, blank=True)
    gpt_reasoning = models.TextField(blank=True)
    
    class Meta:
        db_table = 'user_decision_records'
        indexes = [
            models.Index(fields=['template', 'decision_type']),
            models.Index(fields=['company', 'cage_type']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"Decision {self.decision_id}: {self.decision_type} by {self.user}"


class SymbolGroupAnalysis(models.Model):
    """
    Stores spatial analysis and characteristics of symbol groups
    """
    group_id = models.AutoField(primary_key=True)
    decision_record = models.OneToOneField(UserDecisionRecord, on_delete=models.CASCADE, related_name='group_analysis')
    
    # Group characteristics
    total_symbols_detected = models.IntegerField()
    symbols_accepted = models.IntegerField()
    symbols_rejected = models.IntegerField()
    merge_pattern = models.JSONField(help_text="Which symbols were merged together")
    
    # GPT Vision spatial analysis
    symbols_on_same_line = models.BooleanField()
    horizontal_alignment = models.CharField(max_length=20)  # well_aligned, moderately_aligned, poorly_aligned
    vertical_alignment = models.CharField(max_length=20)    # aligned, moderately_aligned, scattered
    symbol_grouping = models.CharField(max_length=30)       # single_measurement, multiple_measurements, unrelated
    gpt_recommended_action = models.CharField(max_length=20)
    gpt_reasoning = models.TextField()
    
    # Calculated spatial metrics
    bounding_box_area_percent = models.FloatField(help_text="% of segment area covered")
    average_distance_normalized = models.FloatField(help_text="Average distance as % of segment diagonal")
    distance_consistency = models.FloatField(help_text="How uniform the spacing is (0-1)")
    
    # Confidence metrics (no GPT confidence scores)
    spatial_stability = models.FloatField(help_text="How stable the bounding boxes are (0-1)")
    context_consistency = models.FloatField(help_text="How well symbols fit engineering patterns (0-1)")
    isolation_score = models.FloatField(help_text="How isolated/clear the symbols are (0-1)")
    size_consistency = models.FloatField(help_text="How consistent symbol sizes are (0-1)")
    
    class Meta:
        db_table = 'symbol_group_analysis'

    def __str__(self):
        return f"Group {self.group_id}: {self.total_symbols_detected} symbols, {self.gpt_recommended_action}"


class SymbolDecisionContext(models.Model):
    """
    Individual symbol context within a group decision
    """
    DECISION_OUTCOMES = [
        ('ACCEPTED', 'Symbol Accepted'),
        ('REJECTED', 'Symbol Rejected'),
        ('MERGED_WITH_OTHERS', 'Merged with Other Symbols'),
    ]
    
    REJECTION_REASONS = [
        ('LOW_CONFIDENCE', 'Low Confidence Score'),
        ('WRONG_TYPE', 'Wrong Symbol Type'),
        ('DUPLICATE', 'Duplicate Symbol'),
        ('MARGIN_LOCATION', 'Located in Margin Area'),
        ('USER_PREFERENCE', 'User Preference'),
        ('OTHER', 'Other Reason'),
    ]
    
    symbol_context_id = models.AutoField(primary_key=True)
    group_analysis = models.ForeignKey(SymbolGroupAnalysis, on_delete=models.CASCADE, related_name='symbol_contexts')
    
    # Symbol details
    detected_value = models.CharField(max_length=100)
    final_value = models.CharField(max_length=100, help_text="Value after user confirmation/editing")
    symbol_type = models.CharField(max_length=30)
    orientation = models.CharField(max_length=20)  # normal, rotated_90, etc.
    
    # Position & spatial data
    x_coordinate = models.FloatField()
    y_coordinate = models.FloatField()
    width = models.FloatField()
    height = models.FloatField()
    distance_to_nearest_symbol = models.FloatField(null=True, blank=True)
    line_number = models.IntegerField(null=True, blank=True, help_text="Which text line it belongs to")
    cluster_id = models.IntegerField(null=True, blank=True, help_text="Which spatial cluster")
    
    # Decision outcome
    user_decision = models.CharField(max_length=20, choices=DECISION_OUTCOMES)
    merged_with_symbol_ids = models.JSONField(null=True, blank=True, help_text="IDs of symbols this was merged with")
    edit_made = models.BooleanField(default=False, help_text="Did user edit the value")
    rejection_reason = models.CharField(max_length=20, choices=REJECTION_REASONS, null=True, blank=True)
    
    class Meta:
        db_table = 'symbol_decision_context'
        indexes = [
            models.Index(fields=['symbol_type', 'user_decision']),
            models.Index(fields=['group_analysis', 'user_decision']),
        ]

    def __str__(self):
        return f"Symbol {self.symbol_context_id}: {self.detected_value} -> {self.user_decision}"


class TemplateDecisionPattern(models.Model):
    """
    Aggregated decision patterns per template for learning
    """
    pattern_id = models.AutoField(primary_key=True)
    template = models.ForeignKey(Template, on_delete=models.CASCADE)
    
    # Pattern characteristics
    symbol_type_combination = models.CharField(max_length=100, help_text="e.g., 'diameter+tolerance', 'dimension+note'")
    merge_probability = models.FloatField(help_text="% of time users merge this combination")
    spatial_threshold = models.FloatField(help_text="Average distance where users merge vs separate")
    
    # Confidence and reliability
    confidence_threshold = models.FloatField(help_text="Minimum confidence users typically accept")
    sample_size = models.IntegerField(help_text="Number of decisions this pattern is based on")
    accuracy_score = models.FloatField(help_text="How reliable this pattern is (0-1)")
    
    # Common patterns
    common_rejection_reasons = models.JSONField(help_text="Most frequent rejection patterns")
    typical_merge_distance = models.FloatField(help_text="Typical distance for merging in this template")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'template_decision_patterns'
        unique_together = ['template', 'symbol_type_combination']
        indexes = [
            models.Index(fields=['template', 'merge_probability']),
            models.Index(fields=['accuracy_score']),
        ]

    def __str__(self):
        return f"Pattern {self.pattern_id}: {self.template} - {self.symbol_type_combination}"


class SpatialDecisionRule(models.Model):
    """
    Learned spatial rules for automatic decision making
    """
    rule_id = models.AutoField(primary_key=True)
    
    # Context
    template = models.ForeignKey(Template, on_delete=models.CASCADE, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    cage_type = models.ForeignKey(CageType, on_delete=models.CASCADE, null=True, blank=True)
    
    # Rule conditions
    condition = models.JSONField(help_text="Conditions for this rule (same_line, distance_range, symbol_types)")
    action_probability = models.FloatField(help_text="Likelihood of merge/accept/reject")
    confidence_score = models.FloatField(help_text="How reliable this rule is (0-1)")
    sample_size = models.IntegerField(help_text="Number of decisions this rule is based on")
    
    # Rule metadata
    rule_description = models.TextField(help_text="Human-readable description of the rule")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'spatial_decision_rules'
        indexes = [
            models.Index(fields=['template', 'confidence_score']),
            models.Index(fields=['action_probability']),
        ]

    def __str__(self):
        return f"Rule {self.rule_id}: {self.rule_description[:50]}..."


class UserLearningProfile(models.Model):
    """
    User-specific learning patterns and preferences
    """
    profile_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    template = models.ForeignKey(Template, on_delete=models.CASCADE, null=True, blank=True)
    
    # User patterns
    decision_consistency_score = models.FloatField(help_text="How consistent user decisions are (0-1)")
    preferred_merge_distance = models.FloatField(help_text="User's typical merge threshold")
    confidence_tolerance = models.FloatField(help_text="Minimum confidence user accepts")
    processing_speed = models.FloatField(help_text="Average decision time in seconds")
    expertise_level = models.FloatField(help_text="Calculated expertise based on consistency and speed")
    
    # Statistics
    total_decisions = models.IntegerField(default=0)
    merge_rate = models.FloatField(default=0.0, help_text="% of decisions that were merges")
    accuracy_with_gpt_recommendations = models.FloatField(default=0.0, help_text="% agreement with GPT recommendations")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_learning_profiles'
        unique_together = ['user', 'template']
        indexes = [
            models.Index(fields=['user', 'expertise_level']),
            models.Index(fields=['template', 'decision_consistency_score']),
        ]

    def __str__(self):
        return f"Profile {self.profile_id}: {self.user} - {self.template} (expertise: {self.expertise_level:.2f})"
