"""
Decision Learning Service for Process B Pattern Recognition
Captures user decisions and builds patterns for intelligent automation
"""

import time
import math
from django.utils import timezone
from django.db.models import Avg, Count, Q
from ..models_decision_learning import (
    UserDecisionRecord, SymbolGroupAnalysis, SymbolDecisionContext,
    TemplateDecisionPattern, SpatialDecisionRule, UserLearningProfile
)


class DecisionLearningService:
    """
    Service to capture and analyze user decisions for pattern learning
    """
    
    @staticmethod
    def record_user_decision(user, drawing, segment, symbols_data, gpt_analysis, decision_data, processing_time):
        """
        Record a user decision with full context for pattern learning
        
        Args:
            user: User who made the decision
            drawing: Drawing object
            segment: Segment object
            symbols_data: List of symbol data from GPT Vision
            gpt_analysis: GPT Vision spatial analysis
            decision_data: User's decision (accept/reject/merge patterns)
            processing_time: Time taken to make decision
            
        Returns:
            UserDecisionRecord: Created decision record
        """
        try:
            # Create the main decision record
            decision_record = UserDecisionRecord.objects.create(
                user=user,
                drawing=drawing,
                segment=segment,
                template=drawing.template,
                company=getattr(drawing, 'company', None),
                cage_type=getattr(drawing, 'cage_type', None),
                decision_type=decision_data.get('decision_type'),
                processing_time=processing_time,
                user_notes=decision_data.get('notes', ''),
                gpt_recommended_action=gpt_analysis.get('recommended_action', ''),
                gpt_reasoning=gpt_analysis.get('reasoning', '')
            )
            
            # Calculate spatial metrics
            spatial_metrics = DecisionLearningService._calculate_spatial_metrics(
                symbols_data, segment.image.width if hasattr(segment, 'image') else 800,
                segment.image.height if hasattr(segment, 'image') else 600
            )
            
            # Create symbol group analysis
            group_analysis = SymbolGroupAnalysis.objects.create(
                decision_record=decision_record,
                total_symbols_detected=len(symbols_data),
                symbols_accepted=len([s for s in decision_data.get('symbols', []) if s.get('accepted', False)]),
                symbols_rejected=len([s for s in decision_data.get('symbols', []) if not s.get('accepted', False)]),
                merge_pattern=decision_data.get('merge_pattern', {}),
                
                # GPT Vision spatial analysis
                symbols_on_same_line=gpt_analysis.get('symbols_on_same_line', False),
                horizontal_alignment=gpt_analysis.get('horizontal_alignment', 'unknown'),
                vertical_alignment=gpt_analysis.get('vertical_alignment', 'unknown'),
                symbol_grouping=gpt_analysis.get('symbol_grouping', 'unknown'),
                gpt_recommended_action=gpt_analysis.get('recommended_action', 'keep_separate'),
                gpt_reasoning=gpt_analysis.get('reasoning', ''),
                
                # Calculated metrics
                bounding_box_area_percent=spatial_metrics['area_percent'],
                average_distance_normalized=spatial_metrics['avg_distance_normalized'],
                distance_consistency=spatial_metrics['distance_consistency'],
                spatial_stability=spatial_metrics['spatial_stability'],
                context_consistency=spatial_metrics['context_consistency'],
                isolation_score=spatial_metrics['isolation_score'],
                size_consistency=spatial_metrics['size_consistency']
            )
            
            # Create individual symbol contexts
            for i, symbol_data in enumerate(symbols_data):
                user_symbol_decision = decision_data.get('symbols', [{}])[i] if i < len(decision_data.get('symbols', [])) else {}
                
                SymbolDecisionContext.objects.create(
                    group_analysis=group_analysis,
                    detected_value=symbol_data.get('value', ''),
                    final_value=user_symbol_decision.get('final_value', symbol_data.get('value', '')),
                    symbol_type=symbol_data.get('type', 'other'),
                    orientation=symbol_data.get('orientation', 'normal'),
                    x_coordinate=symbol_data.get('x', 0),
                    y_coordinate=symbol_data.get('y', 0),
                    width=symbol_data.get('width', 0),
                    height=symbol_data.get('height', 0),
                    distance_to_nearest_symbol=spatial_metrics.get('distances', [0])[i] if i < len(spatial_metrics.get('distances', [])) else 0,
                    user_decision=user_symbol_decision.get('decision', 'ACCEPTED' if user_symbol_decision.get('accepted', False) else 'REJECTED'),
                    merged_with_symbol_ids=user_symbol_decision.get('merged_with', []),
                    edit_made=user_symbol_decision.get('edited', False),
                    rejection_reason=user_symbol_decision.get('rejection_reason')
                )
            
            # Update learning patterns asynchronously
            DecisionLearningService._update_learning_patterns(decision_record)
            
            return decision_record
            
        except Exception as e:
            print(f"Error recording user decision: {e}")
            return None
    
    @staticmethod
    def _calculate_spatial_metrics(symbols_data, image_width, image_height):
        """
        Calculate spatial metrics for the symbol group
        """
        if not symbols_data:
            return {
                'area_percent': 0, 'avg_distance_normalized': 0, 'distance_consistency': 0,
                'spatial_stability': 0.5, 'context_consistency': 0.5, 'isolation_score': 0.5, 'size_consistency': 0.5,
                'distances': []
            }
        
        # Calculate bounding box area
        if len(symbols_data) > 1:
            min_x = min(s.get('x', 0) for s in symbols_data)
            max_x = max(s.get('x', 0) + s.get('width', 0) for s in symbols_data)
            min_y = min(s.get('y', 0) for s in symbols_data)
            max_y = max(s.get('y', 0) + s.get('height', 0) for s in symbols_data)
            area = (max_x - min_x) * (max_y - min_y)
            area_percent = (area / (image_width * image_height)) * 100
        else:
            symbol = symbols_data[0]
            area = symbol.get('width', 0) * symbol.get('height', 0)
            area_percent = (area / (image_width * image_height)) * 100
        
        # Calculate distances between symbols
        distances = []
        if len(symbols_data) > 1:
            for i in range(len(symbols_data)):
                for j in range(i + 1, len(symbols_data)):
                    s1, s2 = symbols_data[i], symbols_data[j]
                    dx = (s1.get('x', 0) + s1.get('width', 0)/2) - (s2.get('x', 0) + s2.get('width', 0)/2)
                    dy = (s1.get('y', 0) + s1.get('height', 0)/2) - (s2.get('y', 0) + s2.get('height', 0)/2)
                    distance = math.sqrt(dx*dx + dy*dy)
                    distances.append(distance)
        
        # Normalize distances
        diagonal = math.sqrt(image_width*image_width + image_height*image_height)
        avg_distance_normalized = (sum(distances) / len(distances) / diagonal * 100) if distances else 0
        
        # Calculate distance consistency (how uniform spacing is)
        if len(distances) > 1:
            avg_dist = sum(distances) / len(distances)
            variance = sum((d - avg_dist)**2 for d in distances) / len(distances)
            distance_consistency = 1 / (1 + variance / (avg_dist**2)) if avg_dist > 0 else 0
        else:
            distance_consistency = 1.0
        
        return {
            'area_percent': area_percent,
            'avg_distance_normalized': avg_distance_normalized,
            'distance_consistency': distance_consistency,
            'spatial_stability': 0.8,  # Placeholder - would be calculated from bounding box stability
            'context_consistency': 0.7,  # Placeholder - would be calculated from engineering context
            'isolation_score': 0.8,  # Placeholder - would be calculated from surrounding elements
            'size_consistency': 0.9,  # Placeholder - would be calculated from symbol size variations
            'distances': distances
        }
    
    @staticmethod
    def _update_learning_patterns(decision_record):
        """
        Update learning patterns based on new decision
        """
        try:
            # Update template decision patterns
            if decision_record.template:
                DecisionLearningService._update_template_patterns(decision_record)
            
            # Update user learning profile
            if decision_record.user:
                DecisionLearningService._update_user_profile(decision_record)
                
        except Exception as e:
            print(f"Error updating learning patterns: {e}")
    
    @staticmethod
    def _update_template_patterns(decision_record):
        """
        Update template-specific decision patterns
        """
        group_analysis = decision_record.group_analysis
        symbol_contexts = group_analysis.symbol_contexts.all()
        
        # Create symbol type combination string
        symbol_types = sorted([ctx.symbol_type for ctx in symbol_contexts])
        symbol_type_combination = '+'.join(symbol_types)
        
        # Get or create template pattern
        pattern, created = TemplateDecisionPattern.objects.get_or_create(
            template=decision_record.template,
            symbol_type_combination=symbol_type_combination,
            defaults={
                'merge_probability': 0.0,
                'spatial_threshold': 0.0,
                'confidence_threshold': 0.0,
                'sample_size': 0,
                'accuracy_score': 0.0,
                'common_rejection_reasons': {},
                'typical_merge_distance': 0.0
            }
        )
        
        # Update pattern with new data
        all_decisions = UserDecisionRecord.objects.filter(
            template=decision_record.template,
            group_analysis__symbol_contexts__symbol_type__in=symbol_types
        ).distinct()
        
        merge_decisions = all_decisions.filter(decision_type__in=['MERGE_ALL', 'MERGE_PARTIAL']).count()
        total_decisions = all_decisions.count()
        
        pattern.merge_probability = (merge_decisions / total_decisions * 100) if total_decisions > 0 else 0
        pattern.sample_size = total_decisions
        pattern.save()
    
    @staticmethod
    def _update_user_profile(decision_record):
        """
        Update user learning profile
        """
        profile, created = UserLearningProfile.objects.get_or_create(
            user=decision_record.user,
            template=decision_record.template,
            defaults={
                'decision_consistency_score': 0.0,
                'preferred_merge_distance': 0.0,
                'confidence_tolerance': 0.0,
                'processing_speed': 0.0,
                'expertise_level': 0.0,
                'total_decisions': 0,
                'merge_rate': 0.0,
                'accuracy_with_gpt_recommendations': 0.0
            }
        )
        
        # Update profile statistics
        user_decisions = UserDecisionRecord.objects.filter(
            user=decision_record.user,
            template=decision_record.template
        )
        
        profile.total_decisions = user_decisions.count()
        profile.processing_speed = user_decisions.aggregate(avg_time=Avg('processing_time'))['avg_time'] or 0
        
        merge_count = user_decisions.filter(decision_type__in=['MERGE_ALL', 'MERGE_PARTIAL']).count()
        profile.merge_rate = (merge_count / profile.total_decisions * 100) if profile.total_decisions > 0 else 0
        
        # Calculate accuracy with GPT recommendations
        matching_decisions = user_decisions.filter(
            decision_type=models.F('gpt_recommended_action')
        ).count()
        profile.accuracy_with_gpt_recommendations = (matching_decisions / profile.total_decisions * 100) if profile.total_decisions > 0 else 0
        
        # Calculate expertise level (combination of consistency, speed, and accuracy)
        speed_score = max(0, 1 - (profile.processing_speed / 30))  # Faster = higher score
        accuracy_score = profile.accuracy_with_gpt_recommendations / 100
        profile.expertise_level = (speed_score + accuracy_score) / 2
        
        profile.save()
    
    @staticmethod
    def get_automation_recommendation(template, symbol_types, spatial_analysis, user=None):
        """
        Get automation recommendation based on learned patterns
        
        Returns:
            dict: {
                'should_automate': bool,
                'recommended_action': str,
                'confidence': float,
                'reasoning': str
            }
        """
        try:
            # Check template patterns
            symbol_type_combination = '+'.join(sorted(symbol_types))
            
            template_pattern = TemplateDecisionPattern.objects.filter(
                template=template,
                symbol_type_combination=symbol_type_combination
            ).first()
            
            if template_pattern and template_pattern.sample_size >= 5:  # Minimum sample size
                # High confidence automation
                if template_pattern.accuracy_score > 0.9 and template_pattern.sample_size >= 10:
                    action = 'merge_all' if template_pattern.merge_probability > 80 else 'keep_separate'
                    return {
                        'should_automate': True,
                        'recommended_action': action,
                        'confidence': template_pattern.accuracy_score,
                        'reasoning': f'Template pattern: {template_pattern.merge_probability:.1f}% merge rate with {template_pattern.sample_size} samples'
                    }
            
            # Check GPT recommendation with spatial analysis
            gpt_action = spatial_analysis.get('recommended_action', 'keep_separate')
            gpt_reasoning = spatial_analysis.get('reasoning', '')
            
            # Medium confidence based on GPT + spatial factors
            confidence_factors = []
            
            if spatial_analysis.get('symbols_on_same_line') and gpt_action == 'merge_all':
                confidence_factors.append(0.3)
            
            if spatial_analysis.get('horizontal_alignment') == 'well_aligned':
                confidence_factors.append(0.2)
                
            if spatial_analysis.get('symbol_grouping') == 'single_measurement':
                confidence_factors.append(0.3)
            
            total_confidence = sum(confidence_factors)
            
            if total_confidence >= 0.7:  # 70% confidence threshold
                return {
                    'should_automate': True,
                    'recommended_action': gpt_action,
                    'confidence': total_confidence,
                    'reasoning': f'GPT analysis: {gpt_reasoning}'
                }
            
            # Default: present to user
            return {
                'should_automate': False,
                'recommended_action': gpt_action,
                'confidence': total_confidence,
                'reasoning': 'Insufficient confidence for automation - presenting to user'
            }
            
        except Exception as e:
            print(f"Error getting automation recommendation: {e}")
            return {
                'should_automate': False,
                'recommended_action': 'keep_separate',
                'confidence': 0.0,
                'reasoning': 'Error in automation system'
            }
