from django.db import models
from django.contrib.auth.models import User
import os

class Company(models.Model):
    """Stores companies manufacturing templates"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

class CageType(models.Model):
    """Stores different types of cages"""
    name = models.CharField(max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='cage_types')
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.company.name} - {self.name}"

class Template(models.Model):
    """Stores specific templates of a cage type"""
    name = models.CharField(max_length=100)
    cage_type = models.ForeignKey(CageType, on_delete=models.CASCADE, related_name='templates')
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.cage_type.company.name} - {self.cage_type.name} - {self.name}"

def get_drawing_upload_path(instance, filename):
    """Generate upload path for drawing images"""
    # Use a timestamp instead of ID since ID isn't available before save
    import uuid
    import datetime
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    unique_id = str(uuid.uuid4())[:8]

    # Make sure the drawings directory exists
    import os
    from django.conf import settings
    drawings_dir = os.path.join(settings.MEDIA_ROOT, 'drawings')
    os.makedirs(drawings_dir, exist_ok=True)

    return os.path.join('drawings', f"{timestamp}_{unique_id}_{filename}")

def get_segment_upload_path(instance, filename):
    """Generate upload path for segment images"""
    return os.path.join('segments', f"drawing_{instance.drawing.id}", filename)

class Drawing(models.Model):
    """Stores uploaded technical drawings"""
    PROCESS_CHOICES = [
        ('A', 'Process A - Automatic AI Analysis'),
        ('B', 'Process B - Manual Symbol Detection'),
    ]

    image = models.ImageField(upload_to=get_drawing_upload_path)
    original_filename = models.CharField(max_length=255)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processed = models.BooleanField(default=False)
    finalized = models.BooleanField(default=False)
    is_template = models.BooleanField(default=False)
    template = models.ForeignKey(Template, on_delete=models.SET_NULL, null=True, blank=True, related_name='drawings')
    final_image = models.FileField(upload_to='final_outputs', null=True, blank=True)
    finalized_image = models.FileField(upload_to='final_outputs', null=True, blank=True)
    measurements_excel = models.FileField(upload_to='final_outputs', null=True, blank=True)
    summary_file = models.FileField(upload_to='final_outputs', null=True, blank=True)
    process_type = models.CharField(max_length=1, choices=PROCESS_CHOICES, default='A')

    def __str__(self):
        return f"Drawing {self.id}: {self.original_filename}"

class Segment(models.Model):
    """Stores segments of a drawing"""
    drawing = models.ForeignKey(Drawing, on_delete=models.CASCADE, related_name='segments')
    segment_number = models.IntegerField()
    image = models.ImageField(upload_to=get_segment_upload_path)
    grid_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    annotated_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    marked_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    easyocr_debug_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    segment_x_offset = models.IntegerField(default=0)
    segment_y_offset = models.IntegerField(default=0)

    def __str__(self):
        return f"Segment {self.segment_number} of Drawing {self.drawing.id}"

    class Meta:
        ordering = ['segment_number']
        unique_together = ['drawing', 'segment_number']

class Symbol(models.Model):
    """Stores detected symbols within segments"""
    SYMBOL_TYPES = [
        ('diameter', 'Diameter'),
        ('radius', 'Radius'),
        ('tolerance', 'Tolerance'),
        ('degree', 'Degree'),
        ('boxed', 'Boxed Value'),
        ('area', 'Area'),
        ('cross-section', 'Cross-Section'),
        ('dimension', 'Dimension'),
        ('surface_roughness', 'Surface Roughness'),
        ('thread', 'Thread Specification'),
        ('material', 'Material Specification'),
        ('text', 'Text'),
        ('other', 'Other'),
    ]

    POSITION_SOURCES = [
        ('gpt', 'GPT-4.1'),
        ('user', 'User Placed'),
        ('easyocr', 'EasyOCR'),
        ('gpt_vision', 'Gpt_vision'),
        ('distributed', 'Distributed'),
    ]

    ORIENTATION_CHOICES = [
        ('normal', 'Normal'),
        ('rotated', 'Rotated 90°'),
    ]

    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='symbols')
    is_marked = models.BooleanField(default=False)
    symbol_type = models.CharField(max_length=20, choices=SYMBOL_TYPES, default='other')
    value = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    x_coordinate = models.IntegerField()
    y_coordinate = models.IntegerField()
    # Normalized coordinates (0.0 to 1.0) for resolution-independent positioning
    normalized_x = models.FloatField(default=0.0, help_text="X coordinate normalized to image width (0.0 to 1.0)")
    normalized_y = models.FloatField(default=0.0, help_text="Y coordinate normalized to image height (0.0 to 1.0)")
    display_order = models.IntegerField(default=0)
    position_source = models.CharField(max_length=15, choices=POSITION_SOURCES, default='gpt')
    orientation = models.CharField(max_length=10, choices=ORIENTATION_CHOICES, default='normal')

    def __str__(self):
        return f"{self.get_symbol_type_display()}: {self.value} at ({self.x_coordinate}, {self.y_coordinate})"

    class Meta:
        ordering = ['display_order', 'id']

class SegmentSimilarity(models.Model):
    """Stores similarity information between segments"""
    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='similarities')
    similar_segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='similar_to')
    similarity_score = models.FloatField()
    user_confirmed = models.BooleanField(default=False)
    user_rating = models.IntegerField(null=True, blank=True)  # User rating 1-5
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Similarity between Segment {self.segment.id} and {self.similar_segment.id}: {self.similarity_score:.2f}"

    class Meta:
        unique_together = ['segment', 'similar_segment']
        ordering = ['-similarity_score']

class EasyOCRDetection(models.Model):
    """Stores all text detected by EasyOCR (both matched and unmatched)"""
    ORIENTATION_CHOICES = [
        ('normal', 'Normal'),
        ('rotated', 'Rotated 90°'),
    ]

    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='easyocr_detections')
    detected_text = models.CharField(max_length=200)
    confidence = models.FloatField()
    center_x = models.IntegerField()
    center_y = models.IntegerField()
    # Normalized coordinates (0.0 to 1.0) for resolution-independent positioning
    normalized_x = models.FloatField(default=0.0, help_text="X coordinate normalized to image width (0.0 to 1.0)")
    normalized_y = models.FloatField(default=0.0, help_text="Y coordinate normalized to image height (0.0 to 1.0)")
    orientation = models.CharField(max_length=10, choices=ORIENTATION_CHOICES, default='normal')
    matched_symbol = models.ForeignKey(Symbol, on_delete=models.SET_NULL, null=True, blank=True, related_name='easyocr_detections')
    similarity_score = models.FloatField(default=0.0)
    is_matched = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        match_status = f"→ {self.matched_symbol.value}" if self.matched_symbol else "(unmatched)"
        return f"EasyOCR: '{self.detected_text}' {match_status} in Segment {self.segment.segment_number}"

    class Meta:
        ordering = ['-confidence', 'detected_text']

class BoundingBox(models.Model):
    """Stores bounding boxes detected by EasyOCR for Process B"""
    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='bounding_boxes')
    x = models.IntegerField(help_text="Top-left X coordinate")
    y = models.IntegerField(help_text="Top-left Y coordinate")
    width = models.IntegerField(help_text="Width of bounding box")
    height = models.IntegerField(help_text="Height of bounding box")
    center_x = models.IntegerField(help_text="Center X coordinate")
    center_y = models.IntegerField(help_text="Center Y coordinate")
    confidence = models.FloatField(default=0.0)
    detected_text = models.CharField(max_length=200, blank=True)
    processed = models.BooleanField(default=False, help_text="Whether this box has been processed by GPT Vision")
    confirmed_symbol = models.ForeignKey(Symbol, on_delete=models.SET_NULL, null=True, blank=True, related_name='source_bounding_box')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"BoundingBox {self.id} in Segment {self.segment.segment_number}: ({self.x}, {self.y}, {self.width}x{self.height})"

    class Meta:
        ordering = ['x', 'y']

class AnalysisLog(models.Model):
    """Stores logs of the analysis process"""
    drawing = models.ForeignKey(Drawing, on_delete=models.CASCADE, related_name='logs')
    timestamp = models.DateTimeField(auto_now_add=True)
    message = models.TextField()

    def __str__(self):
        return f"Log for Drawing {self.drawing.id}: {self.timestamp}"

    class Meta:
        ordering = ['timestamp']


# Decision Learning Models for Process B Pattern Recognition
class UserDecisionRecord(models.Model):
    """
    Records every user decision for pattern learning
    """
    DECISION_TYPES = [
        ('ACCEPT_SINGLE', 'Accept Single Symbol'),
        ('REJECT_SINGLE', 'Reject Single Symbol'),
        ('MERGE_ALL', 'Merge All Symbols'),
        ('MERGE_PARTIAL', 'Merge Some Symbols'),
        ('REJECT_ALL', 'Reject All Symbols'),
    ]

    # Decision metadata
    decision_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    # Context
    drawing = models.ForeignKey(Drawing, on_delete=models.CASCADE)
    segment = models.ForeignKey(Segment, on_delete=models.CASCADE)
    template = models.ForeignKey(Template, on_delete=models.SET_NULL, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True)
    cage_type = models.ForeignKey(CageType, on_delete=models.SET_NULL, null=True, blank=True)

    # Decision details
    decision_type = models.CharField(max_length=20, choices=DECISION_TYPES)
    processing_time = models.FloatField(help_text="Time taken to make decision in seconds")
    user_notes = models.TextField(blank=True, help_text="Optional user comments")

    # GPT Vision context
    gpt_recommended_action = models.CharField(max_length=20, blank=True)
    gpt_reasoning = models.TextField(blank=True)

    class Meta:
        db_table = 'user_decision_records'
        indexes = [
            models.Index(fields=['template', 'decision_type']),
            models.Index(fields=['company', 'cage_type']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        return f"Decision {self.decision_id}: {self.decision_type} by {self.user}"


class SymbolGroupAnalysis(models.Model):
    """
    Stores spatial analysis and characteristics of symbol groups
    """
    group_id = models.AutoField(primary_key=True)
    decision_record = models.OneToOneField(UserDecisionRecord, on_delete=models.CASCADE, related_name='group_analysis')

    # Group characteristics
    total_symbols_detected = models.IntegerField()
    symbols_accepted = models.IntegerField()
    symbols_rejected = models.IntegerField()
    merge_pattern = models.JSONField(help_text="Which symbols were merged together")

    # GPT Vision spatial analysis
    symbols_on_same_line = models.BooleanField()
    horizontal_alignment = models.CharField(max_length=20)  # well_aligned, moderately_aligned, poorly_aligned
    vertical_alignment = models.CharField(max_length=20)    # aligned, moderately_aligned, scattered
    symbol_grouping = models.CharField(max_length=30)       # single_measurement, multiple_measurements, unrelated
    gpt_recommended_action = models.CharField(max_length=20)
    gpt_reasoning = models.TextField()

    # Calculated spatial metrics
    bounding_box_area_percent = models.FloatField(help_text="% of segment area covered")
    average_distance_normalized = models.FloatField(help_text="Average distance as % of segment diagonal")
    distance_consistency = models.FloatField(help_text="How uniform the spacing is (0-1)")

    # Confidence metrics (no GPT confidence scores)
    spatial_stability = models.FloatField(help_text="How stable the bounding boxes are (0-1)")
    context_consistency = models.FloatField(help_text="How well symbols fit engineering patterns (0-1)")
    isolation_score = models.FloatField(help_text="How isolated/clear the symbols are (0-1)")
    size_consistency = models.FloatField(help_text="How consistent symbol sizes are (0-1)")

    class Meta:
        db_table = 'symbol_group_analysis'

    def __str__(self):
        return f"Group {self.group_id}: {self.total_symbols_detected} symbols, {self.gpt_recommended_action}"


class TemplateDecisionPattern(models.Model):
    """
    Aggregated decision patterns per template for learning
    """
    pattern_id = models.AutoField(primary_key=True)
    template = models.ForeignKey(Template, on_delete=models.CASCADE)

    # Pattern characteristics
    symbol_type_combination = models.CharField(max_length=100, help_text="e.g., 'diameter+tolerance', 'dimension+note'")
    merge_probability = models.FloatField(help_text="% of time users merge this combination")
    spatial_threshold = models.FloatField(help_text="Average distance where users merge vs separate")

    # Confidence and reliability
    confidence_threshold = models.FloatField(help_text="Minimum confidence users typically accept")
    sample_size = models.IntegerField(help_text="Number of decisions this pattern is based on")
    accuracy_score = models.FloatField(help_text="How reliable this pattern is (0-1)")

    # Common patterns
    common_rejection_reasons = models.JSONField(help_text="Most frequent rejection patterns")
    typical_merge_distance = models.FloatField(help_text="Typical distance for merging in this template")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'template_decision_patterns'
        unique_together = ['template', 'symbol_type_combination']
        indexes = [
            models.Index(fields=['template', 'merge_probability']),
            models.Index(fields=['accuracy_score']),
        ]

    def __str__(self):
        return f"Pattern {self.pattern_id}: {self.template} - {self.symbol_type_combination}"


class UserLearningProfile(models.Model):
    """
    User-specific learning patterns and preferences
    """
    profile_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    template = models.ForeignKey(Template, on_delete=models.CASCADE, null=True, blank=True)

    # User patterns
    decision_consistency_score = models.FloatField(help_text="How consistent user decisions are (0-1)")
    preferred_merge_distance = models.FloatField(help_text="User's typical merge threshold")
    confidence_tolerance = models.FloatField(help_text="Minimum confidence user accepts")
    processing_speed = models.FloatField(help_text="Average decision time in seconds")
    expertise_level = models.FloatField(help_text="Calculated expertise based on consistency and speed")

    # Statistics
    total_decisions = models.IntegerField(default=0)
    merge_rate = models.FloatField(default=0.0, help_text="% of decisions that were merges")
    accuracy_with_gpt_recommendations = models.FloatField(default=0.0, help_text="% agreement with GPT recommendations")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_learning_profiles'
        unique_together = ['user', 'template']
        indexes = [
            models.Index(fields=['user', 'expertise_level']),
            models.Index(fields=['template', 'decision_consistency_score']),
        ]

    def __str__(self):
        return f"Profile {self.profile_id}: {self.user} - {self.template} (expertise: {self.expertise_level:.2f})"
