from django.db import models
import os

class Company(models.Model):
    """Stores companies manufacturing templates"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

class CageType(models.Model):
    """Stores different types of cages"""
    name = models.CharField(max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='cage_types')
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.company.name} - {self.name}"

class Template(models.Model):
    """Stores specific templates of a cage type"""
    name = models.CharField(max_length=100)
    cage_type = models.ForeignKey(CageType, on_delete=models.CASCADE, related_name='templates')
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.cage_type.company.name} - {self.cage_type.name} - {self.name}"

def get_drawing_upload_path(instance, filename):
    """Generate upload path for drawing images"""
    # Use a timestamp instead of ID since ID isn't available before save
    import uuid
    import datetime
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    unique_id = str(uuid.uuid4())[:8]

    # Make sure the drawings directory exists
    import os
    from django.conf import settings
    drawings_dir = os.path.join(settings.MEDIA_ROOT, 'drawings')
    os.makedirs(drawings_dir, exist_ok=True)

    return os.path.join('drawings', f"{timestamp}_{unique_id}_{filename}")

def get_segment_upload_path(instance, filename):
    """Generate upload path for segment images"""
    return os.path.join('segments', f"drawing_{instance.drawing.id}", filename)

class Drawing(models.Model):
    """Stores uploaded technical drawings"""
    PROCESS_CHOICES = [
        ('A', 'Process A - Automatic AI Analysis'),
        ('B', 'Process B - Manual Symbol Detection'),
    ]

    image = models.ImageField(upload_to=get_drawing_upload_path)
    original_filename = models.CharField(max_length=255)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processed = models.BooleanField(default=False)
    finalized = models.BooleanField(default=False)
    is_template = models.BooleanField(default=False)
    template = models.ForeignKey(Template, on_delete=models.SET_NULL, null=True, blank=True, related_name='drawings')
    final_image = models.FileField(upload_to='final_outputs', null=True, blank=True)
    finalized_image = models.FileField(upload_to='final_outputs', null=True, blank=True)
    measurements_excel = models.FileField(upload_to='final_outputs', null=True, blank=True)
    summary_file = models.FileField(upload_to='final_outputs', null=True, blank=True)
    process_type = models.CharField(max_length=1, choices=PROCESS_CHOICES, default='A')

    def __str__(self):
        return f"Drawing {self.id}: {self.original_filename}"

class Segment(models.Model):
    """Stores segments of a drawing"""
    drawing = models.ForeignKey(Drawing, on_delete=models.CASCADE, related_name='segments')
    segment_number = models.IntegerField()
    image = models.ImageField(upload_to=get_segment_upload_path)
    grid_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    annotated_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    marked_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    easyocr_debug_image = models.ImageField(upload_to=get_segment_upload_path, null=True, blank=True)
    segment_x_offset = models.IntegerField(default=0)
    segment_y_offset = models.IntegerField(default=0)

    def __str__(self):
        return f"Segment {self.segment_number} of Drawing {self.drawing.id}"

    class Meta:
        ordering = ['segment_number']
        unique_together = ['drawing', 'segment_number']

class Symbol(models.Model):
    """Stores detected symbols within segments"""
    SYMBOL_TYPES = [
        ('diameter', 'Diameter'),
        ('radius', 'Radius'),
        ('tolerance', 'Tolerance'),
        ('degree', 'Degree'),
        ('boxed', 'Boxed Value'),
        ('area', 'Area'),
        ('cross-section', 'Cross-Section'),
        ('dimension', 'Dimension'),
        ('surface_roughness', 'Surface Roughness'),
        ('thread', 'Thread Specification'),
        ('material', 'Material Specification'),
        ('text', 'Text'),
        ('other', 'Other'),
    ]

    POSITION_SOURCES = [
        ('gpt', 'GPT-4.1'),
        ('user', 'User Placed'),
        ('easyocr', 'EasyOCR'),
        ('gpt_vision', 'Gpt_vision'),
        ('distributed', 'Distributed'),
    ]

    ORIENTATION_CHOICES = [
        ('normal', 'Normal'),
        ('rotated', 'Rotated 90°'),
    ]

    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='symbols')
    is_marked = models.BooleanField(default=False)
    symbol_type = models.CharField(max_length=20, choices=SYMBOL_TYPES, default='other')
    value = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    x_coordinate = models.IntegerField()
    y_coordinate = models.IntegerField()
    # Normalized coordinates (0.0 to 1.0) for resolution-independent positioning
    normalized_x = models.FloatField(default=0.0, help_text="X coordinate normalized to image width (0.0 to 1.0)")
    normalized_y = models.FloatField(default=0.0, help_text="Y coordinate normalized to image height (0.0 to 1.0)")
    display_order = models.IntegerField(default=0)
    position_source = models.CharField(max_length=15, choices=POSITION_SOURCES, default='gpt')
    orientation = models.CharField(max_length=10, choices=ORIENTATION_CHOICES, default='normal')

    def __str__(self):
        return f"{self.get_symbol_type_display()}: {self.value} at ({self.x_coordinate}, {self.y_coordinate})"

    class Meta:
        ordering = ['display_order', 'id']

class SegmentSimilarity(models.Model):
    """Stores similarity information between segments"""
    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='similarities')
    similar_segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='similar_to')
    similarity_score = models.FloatField()
    user_confirmed = models.BooleanField(default=False)
    user_rating = models.IntegerField(null=True, blank=True)  # User rating 1-5
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Similarity between Segment {self.segment.id} and {self.similar_segment.id}: {self.similarity_score:.2f}"

    class Meta:
        unique_together = ['segment', 'similar_segment']
        ordering = ['-similarity_score']

class EasyOCRDetection(models.Model):
    """Stores all text detected by EasyOCR (both matched and unmatched)"""
    ORIENTATION_CHOICES = [
        ('normal', 'Normal'),
        ('rotated', 'Rotated 90°'),
    ]

    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='easyocr_detections')
    detected_text = models.CharField(max_length=200)
    confidence = models.FloatField()
    center_x = models.IntegerField()
    center_y = models.IntegerField()
    # Normalized coordinates (0.0 to 1.0) for resolution-independent positioning
    normalized_x = models.FloatField(default=0.0, help_text="X coordinate normalized to image width (0.0 to 1.0)")
    normalized_y = models.FloatField(default=0.0, help_text="Y coordinate normalized to image height (0.0 to 1.0)")
    orientation = models.CharField(max_length=10, choices=ORIENTATION_CHOICES, default='normal')
    matched_symbol = models.ForeignKey(Symbol, on_delete=models.SET_NULL, null=True, blank=True, related_name='easyocr_detections')
    similarity_score = models.FloatField(default=0.0)
    is_matched = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        match_status = f"→ {self.matched_symbol.value}" if self.matched_symbol else "(unmatched)"
        return f"EasyOCR: '{self.detected_text}' {match_status} in Segment {self.segment.segment_number}"

    class Meta:
        ordering = ['-confidence', 'detected_text']

class BoundingBox(models.Model):
    """Stores bounding boxes detected by EasyOCR for Process B"""
    segment = models.ForeignKey(Segment, on_delete=models.CASCADE, related_name='bounding_boxes')
    x = models.IntegerField(help_text="Top-left X coordinate")
    y = models.IntegerField(help_text="Top-left Y coordinate")
    width = models.IntegerField(help_text="Width of bounding box")
    height = models.IntegerField(help_text="Height of bounding box")
    center_x = models.IntegerField(help_text="Center X coordinate")
    center_y = models.IntegerField(help_text="Center Y coordinate")
    confidence = models.FloatField(default=0.0)
    detected_text = models.CharField(max_length=200, blank=True)
    processed = models.BooleanField(default=False, help_text="Whether this box has been processed by GPT Vision")
    confirmed_symbol = models.ForeignKey(Symbol, on_delete=models.SET_NULL, null=True, blank=True, related_name='source_bounding_box')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"BoundingBox {self.id} in Segment {self.segment.segment_number}: ({self.x}, {self.y}, {self.width}x{self.height})"

    class Meta:
        ordering = ['x', 'y']

class AnalysisLog(models.Model):
    """Stores logs of the analysis process"""
    drawing = models.ForeignKey(Drawing, on_delete=models.CASCADE, related_name='logs')
    timestamp = models.DateTimeField(auto_now_add=True)
    message = models.TextField()

    def __str__(self):
        return f"Log for Drawing {self.drawing.id}: {self.timestamp}"

    class Meta:
        ordering = ['timestamp']
