#!/usr/bin/env python3
"""
Test script to verify Azure OpenAI API credentials and endpoints
"""

import requests
import json
import base64
from PIL import Image, ImageDraw
import io

# Test endpoints from the code
LLM_ENDPOINTS = [
    {
        'name': 'GPT-4o-Primary',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACHYHv6XJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    {
        'name': 'GPT-4.1-Secondary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BBACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1'
    },
    {
        'name': 'GPT-4o-Secondary',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACYeBjFXJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    {
        'name': 'GPT-4.1-Tertiary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1-2/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BBACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1'
    }
]

def create_test_image():
    """Create a simple test image with text"""
    # Create a simple image with text
    img = Image.new('RGB', (200, 100), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((10, 40), "Ø25.4 ±0.05", fill='black')
    
    # Convert to base64
    buffered = io.BytesIO()
    img.save(buffered, format="PNG")
    img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
    
    return img_base64

def test_endpoint(endpoint_config):
    """Test a single endpoint"""
    print(f"\n🧪 Testing {endpoint_config['name']}...")
    print(f"   Endpoint: {endpoint_config['endpoint']}")
    print(f"   Model: {endpoint_config['model']}")
    
    try:
        # Create test image
        test_image = create_test_image()
        
        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'api-key': endpoint_config['api_key']
        }
        
        # Simple text-only test first
        text_payload = {
            "model": endpoint_config['model'],
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, can you respond with just 'API Working'?"
                }
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        print("   Testing text-only request...")
        response = requests.post(
            endpoint_config['endpoint'],
            headers=headers,
            json=text_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print("   ✅ Text API working!")
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"   Response: {content}")
            
            # Now test vision capabilities
            print("   Testing vision capabilities...")
            vision_payload = {
                "model": endpoint_config['model'],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "What text do you see in this image? Respond with just the text."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{test_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 50,
                "temperature": 0.1
            }
            
            vision_response = requests.post(
                endpoint_config['endpoint'],
                headers=headers,
                json=vision_payload,
                timeout=30
            )
            
            if vision_response.status_code == 200:
                print("   ✅ Vision API working!")
                vision_result = vision_response.json()
                vision_content = vision_result['choices'][0]['message']['content']
                print(f"   Vision Response: {vision_content}")
                return True
            else:
                print(f"   ❌ Vision API failed: {vision_response.status_code}")
                print(f"   Error: {vision_response.text}")
                return False
                
        else:
            print(f"   ❌ Text API failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False

def main():
    """Test all endpoints"""
    print("🔍 Testing Azure OpenAI API Credentials and Endpoints")
    print("=" * 60)
    
    working_endpoints = []
    failed_endpoints = []
    
    for endpoint_config in LLM_ENDPOINTS:
        if test_endpoint(endpoint_config):
            working_endpoints.append(endpoint_config['name'])
        else:
            failed_endpoints.append(endpoint_config['name'])
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if working_endpoints:
        print(f"✅ Working endpoints ({len(working_endpoints)}):")
        for name in working_endpoints:
            print(f"   - {name}")
    
    if failed_endpoints:
        print(f"\n❌ Failed endpoints ({len(failed_endpoints)}):")
        for name in failed_endpoints:
            print(f"   - {name}")
    
    if not working_endpoints:
        print("\n🚨 ALL ENDPOINTS FAILED!")
        print("Possible issues:")
        print("   1. API keys have expired")
        print("   2. Azure subscription suspended")
        print("   3. Deployment names changed")
        print("   4. Rate limits exceeded")
        print("   5. Network connectivity issues")
    else:
        print(f"\n✅ {len(working_endpoints)} out of {len(LLM_ENDPOINTS)} endpoints working")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
