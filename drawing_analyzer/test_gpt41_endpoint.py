#!/usr/bin/env python3
"""
Test the GPT-4.1-2 endpoint specifically with the corrected model name
"""

import requests
import json
import base64
from PIL import Image, ImageDraw
import io

def create_test_image():
    """Create a simple test image with text"""
    img = Image.new('RGB', (200, 100), color='white')
    draw = ImageDraw.Draw(img)
    draw.text((10, 40), "Ø25.4 ±0.05", fill='black')
    
    # Convert to base64
    buffered = io.BytesIO()
    img.save(buffered, format="PNG")
    img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
    
    return img_base64

def test_gpt41_endpoint():
    """Test the GPT-4.1-2 endpoint with corrected model name"""
    print("🧪 Testing GPT-4.1-2 Endpoint with Corrected Model Name")
    print("=" * 60)
    
    endpoint_config = {
        'name': 'GPT-4.1-Tertiary',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1-2/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BBACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1-2'  # Corrected model name
    }
    
    print(f"Endpoint: {endpoint_config['endpoint']}")
    print(f"Model: {endpoint_config['model']}")
    
    try:
        # Test 1: Simple text request
        print("\n📝 Test 1: Simple text request...")
        headers = {
            'Content-Type': 'application/json',
            'api-key': endpoint_config['api_key']
        }
        
        text_payload = {
            "model": endpoint_config['model'],
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, respond with 'GPT-4.1-2 Working'"
                }
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        response = requests.post(
            endpoint_config['endpoint'],
            headers=headers,
            json=text_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ Text API working!")
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"Response: {content}")
            
            # Test 2: Vision capabilities
            print("\n🔍 Test 2: Vision capabilities...")
            test_image = create_test_image()
            
            vision_payload = {
                "model": endpoint_config['model'],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "What engineering symbols do you see? Respond with JSON: {\"values\": [\"symbol1\", \"symbol2\"], \"symbol_types\": [\"type1\", \"type2\"]}"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{test_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 100,
                "temperature": 0.1
            }
            
            vision_response = requests.post(
                endpoint_config['endpoint'],
                headers=headers,
                json=vision_payload,
                timeout=30
            )
            
            if vision_response.status_code == 200:
                print("✅ Vision API working!")
                vision_result = vision_response.json()
                vision_content = vision_result['choices'][0]['message']['content']
                print(f"Vision Response: {vision_content}")
                
                # Test 3: Enhanced spatial analysis prompt
                print("\n🎯 Test 3: Enhanced spatial analysis...")
                spatial_payload = {
                    "model": endpoint_config['model'],
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": """Analyze this engineering drawing image and return JSON:
{
    "values": ["value1", "value2"],
    "symbol_types": ["type1", "type2"],
    "spatial_analysis": {
        "symbols_on_same_line": true,
        "horizontal_alignment": "well_aligned",
        "recommended_action": "merge_all",
        "reasoning": "explanation"
    }
}"""
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{test_image}"
                                    }
                                }
                            ]
                        }
                    ],
                    "max_tokens": 200,
                    "temperature": 0.1
                }
                
                spatial_response = requests.post(
                    endpoint_config['endpoint'],
                    headers=headers,
                    json=spatial_payload,
                    timeout=30
                )
                
                if spatial_response.status_code == 200:
                    print("✅ Enhanced spatial analysis working!")
                    spatial_result = spatial_response.json()
                    spatial_content = spatial_result['choices'][0]['message']['content']
                    print(f"Spatial Analysis Response: {spatial_content}")
                    
                    print("\n🎉 GPT-4.1-2 ENDPOINT IS FULLY FUNCTIONAL!")
                    print("✅ Text processing: Working")
                    print("✅ Vision capabilities: Working") 
                    print("✅ Spatial analysis: Working")
                    print("\n💡 This endpoint can be re-enabled for load balancing!")
                    
                    return True
                else:
                    print(f"❌ Spatial analysis failed: {spatial_response.status_code}")
                    print(f"Error: {spatial_response.text}")
                    return False
            else:
                print(f"❌ Vision API failed: {vision_response.status_code}")
                print(f"Error: {vision_response.text}")
                return False
        else:
            print(f"❌ Text API failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    print("🔍 Testing GPT-4.1-2 Endpoint for Re-enabling")
    print("=" * 60)
    
    if test_gpt41_endpoint():
        print("\n" + "=" * 60)
        print("✅ RECOMMENDATION: RE-ENABLE GPT-4.1-2 ENDPOINT")
        print("=" * 60)
        print("The GPT-4.1-2 endpoint is working perfectly!")
        print("You can uncomment it in the LLM_ENDPOINTS array for:")
        print("  - Load balancing")
        print("  - Faster processing")
        print("  - Redundancy")
    else:
        print("\n" + "=" * 60)
        print("❌ RECOMMENDATION: KEEP GPT-4.1-2 DISABLED")
        print("=" * 60)
        print("The endpoint is still not working properly.")

if __name__ == "__main__":
    main()
