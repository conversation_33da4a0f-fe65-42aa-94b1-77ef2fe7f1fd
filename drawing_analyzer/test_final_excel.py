#!/usr/bin/env python3
"""
Final test to create an Excel file with proper Unicode handling for Windows.
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import os

def create_final_test_excel():
    """Create a final test Excel file with Unicode characters and proper formatting."""
    
    # Create a new workbook and select the active worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Measurements"

    # Define column headers
    headers = ["S.No.", "Measurement", "Min Value", "Max Value", "Instrument Value (Yes/No)", "Remarks"]

    # Set column headers with styling
    header_fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
    header_font = Font(bold=True, name='Cal<PERSON>ri', size=11)
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_idx, value=header)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = thin_border

    # Test data with problematic Unicode characters that need fixing
    test_data_raw = [
        (1, "â‚¬20.7", "â‚¬20.7", "â‚¬20.7", "Yes", ""),
        (2, "â‚¬19-23", "â‚¬19-23", "â‚¬19-23", "Yes", ""),
        (3, "R0.45", "0.45", "0.45", "Yes", ""),
        (4, "45Â°", "45Â°", "45Â°", "Yes", ""),
        (5, "0.2", "0.2", "0.2", "Yes", ""),
        (6, "0.3R", "0.3R", "0.3R", "Yes", ""),
        (7, "â‚¬22.08", "â‚¬22.08", "â‚¬22.08", "Yes", ""),
        (8, "â‚¬1.40Â±0.05", "1.35", "1.45", "Yes", ""),
        (9, "175P.C.D.", "175P.C.D.", "175P.C.D.", "Yes", ""),
        (10, "0.1", "0.1", "0.1", "Yes", ""),
        (11, "0.2", "0.2", "0.2", "Yes", ""),
        (12, "Â°38", "38", "38", "Yes", ""),
        (13, "0.85+0.04-0.04", "0.81", "0.89", "Yes", ""),
        (14, "0.60-0.16", "0.44", "0.6", "Yes", ""),
        (15, "â‚¬15.1Â±0.04", "15.06", "15.14", "Yes", ""),
    ]

    # Function to clean Unicode characters
    def clean_unicode_text(text):
        """Clean and normalize Unicode characters for better Excel compatibility."""
        if not isinstance(text, str):
            return str(text)
            
        # Fix diameter symbol variations
        text = text.replace('\u00e2\u0082\u00ac', '\u00d8')  # â‚¬ -> Ø
        text = text.replace('â‚¬', 'Ø')                      # Direct replacement
        text = text.replace('âˆ…', 'Ø')                      # Another diameter variant
        
        # Fix plus-minus symbol variations  
        text = text.replace('\u00c2\u00b1', '\u00b1')        # Â± -> ±
        text = text.replace('Â±', '±')                       # Direct replacement
        
        # Fix degree symbol variations
        text = text.replace('\u00c2\u00b0', '\u00b0')        # Â° -> °
        text = text.replace('Â°', '°')                       # Direct replacement
        
        # Fix minus symbol variations
        text = text.replace('\u00e2\u0088\u0092', '-')       # âˆ' -> -
        text = text.replace('\u2212', '-')                   # Unicode minus -> -
        
        # Additional common encoding fixes for quotes and other characters
        text = text.replace('\u201c', '"')                   # Left double quote
        text = text.replace('\u201d', '"')                   # Right double quote
        text = text.replace('\u2019', "'")                   # Right single quote
        text = text.replace('\u2018', "'")                   # Left single quote
        
        return text

    # Add test data with Unicode cleaning
    cell_font = Font(name='Calibri', size=10)
    center_alignment = Alignment(horizontal='center', vertical='center')
    
    for row_idx, (s_no, measurement, min_val, max_val, instrument, remarks) in enumerate(test_data_raw, 2):
        # Clean the Unicode characters
        measurement_cleaned = clean_unicode_text(measurement)
        min_val_cleaned = clean_unicode_text(min_val)
        max_val_cleaned = clean_unicode_text(max_val)
        
        # Add data to cells
        s_no_cell = ws.cell(row=row_idx, column=1, value=s_no)
        measurement_cell = ws.cell(row=row_idx, column=2, value=measurement_cleaned)
        min_cell = ws.cell(row=row_idx, column=3, value=min_val_cleaned)
        max_cell = ws.cell(row=row_idx, column=4, value=max_val_cleaned)
        instrument_cell = ws.cell(row=row_idx, column=5, value=instrument)
        remarks_cell = ws.cell(row=row_idx, column=6, value=remarks)
        
        # Apply formatting
        for cell in [s_no_cell, measurement_cell, min_cell, max_cell, instrument_cell, remarks_cell]:
            cell.font = cell_font
            cell.border = thin_border
            
        # Center align numeric columns
        s_no_cell.alignment = center_alignment
        min_cell.alignment = center_alignment
        max_cell.alignment = center_alignment
        instrument_cell.alignment = center_alignment

    # Add data validation for Yes/No dropdown
    if len(test_data_raw) > 0:
        dv = openpyxl.worksheet.datavalidation.DataValidation(
            type="list",
            formula1='"Yes,No"',
            allow_blank=True
        )
        dv.add(f'E2:E{len(test_data_raw)+1}')
        ws.add_data_validation(dv)

    # Auto-adjust column widths
    column_widths = [8, 20, 12, 12, 18, 15]
    for col_idx, width in enumerate(column_widths, 1):
        column_letter = get_column_letter(col_idx)
        ws.column_dimensions[column_letter].width = width

    # Save the workbook
    excel_path = os.path.join('/home/<USER>/manu/drawing_analyzer/media', 'final_unicode_test.xlsx')
    
    try:
        wb.save(excel_path)
        print(f"✓ Final Excel file created successfully: {excel_path}")
        
        # Check file size
        if os.path.exists(excel_path):
            file_size = os.path.getsize(excel_path)
            print(f"✓ File size: {file_size} bytes")
            
            # Try to read it back to verify
            try:
                wb_test = openpyxl.load_workbook(excel_path)
                ws_test = wb_test.active
                print(f"✓ Excel file is readable")
                print(f"✓ Worksheet title: {ws_test.title}")
                print(f"✓ Number of rows: {ws_test.max_row}")
                
                # Show some sample data
                print("\nSample data from Excel file:")
                for row in range(1, min(6, ws_test.max_row + 1)):
                    row_data = []
                    for col in range(1, ws_test.max_column + 1):
                        cell_value = ws_test.cell(row=row, column=col).value
                        row_data.append(str(cell_value) if cell_value is not None else "")
                    print(f"  Row {row}: {row_data}")
                    
            except Exception as e:
                print(f"✗ Error reading Excel file: {e}")
        
        return excel_path
    except Exception as e:
        print(f"✗ Error creating Excel file: {e}")
        return None

if __name__ == "__main__":
    print("=" * 70)
    print("FINAL UNICODE EXCEL TEST FOR WINDOWS COMPATIBILITY")
    print("=" * 70)
    
    excel_file = create_final_test_excel()
    
    if excel_file:
        print(f"\n✓ SUCCESS: Excel file created at {excel_file}")
        print("\nThis file should now display Unicode characters correctly in Excel on Windows.")
        print("The following characters have been fixed:")
        print("  - â‚¬ → Ø (diameter symbol)")
        print("  - Â± → ± (plus-minus symbol)")
        print("  - Â° → ° (degree symbol)")
        print("  - Various quote marks and other symbols")
    else:
        print("\n✗ FAILED: Could not create Excel file.")
    
    print("\n" + "=" * 70)
    print("TEST COMPLETED")
    print("=" * 70)
