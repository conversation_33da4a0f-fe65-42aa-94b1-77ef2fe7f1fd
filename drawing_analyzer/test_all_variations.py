#!/usr/bin/env python3
"""
Test all possible endpoint variations to find working ones
"""

import requests
import json

# Test different API versions and configurations for the working key
test_endpoints = [
    # Working endpoint (confirmed)
    {
        'name': 'GPT-4o-Secondary-Working',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACHYHv6XJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    # Try different API versions for the same endpoint
    {
        'name': 'GPT-4o-Secondary-2024-API',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACHYHv6XJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    {
        'name': 'GPT-4o-Secondary-2024-06-API',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-06-01',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACHYHv6XJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    # Try the other key with different endpoints
    {
        'name': 'GPT-4.1-Key-with-GPT4o-Endpoint',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BCACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4o'
    },
    # Try if there are other deployments on the working endpoint
    {
        'name': 'GPT-4o-Primary-Fixed-URL',
        'endpoint': 'https://ai-aihub2573706963054.services.ai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview',
        'api_key': '4ZKiVgYHfBBHIHijqHMVtE6xh5ABLfFslHtElxGLuMZwRL839BI2JQQJ99BBACYeBjFXJ3w3AAAAACOGpNZC',
        'model': 'gpt-4o'
    },
    # Try corrected GPT-4.1-2 again
    {
        'name': 'GPT-4.1-2-Corrected',
        'endpoint': 'https://dashm-m88kj25m-eastus2.openai.azure.com/openai/deployments/gpt-4.1-2/chat/completions?api-version=2025-01-01-preview',
        'api_key': 'EyHOtNY9W8xZeYgH9JZ4MsRCaq3aCAIYb3sMcEsAiz1ZVCZ8NHyMJQQJ99BCACHYHv6XJ3w3AAAAACOGw8iG',
        'model': 'gpt-4.1-2'
    }
]

def test_endpoint(endpoint_config):
    """Test a single endpoint"""
    print(f"\n🧪 Testing {endpoint_config['name']}...")
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'api-key': endpoint_config['api_key']
        }
        
        payload = {
            "model": endpoint_config['model'],
            "messages": [
                {
                    "role": "user",
                    "content": "Hello"
                }
            ],
            "max_tokens": 5,
            "temperature": 0.1
        }
        
        response = requests.post(
            endpoint_config['endpoint'],
            headers=headers,
            json=payload,
            timeout=15
        )
        
        if response.status_code == 200:
            print(f"   ✅ Working! Response: {response.json()['choices'][0]['message']['content']}")
            return True
        else:
            print(f"   ❌ Failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False

def main():
    print("🔍 Testing All Endpoint Variations for Load Balancing")
    print("=" * 70)
    
    working_endpoints = []
    
    for endpoint_config in test_endpoints:
        if test_endpoint(endpoint_config):
            working_endpoints.append(endpoint_config)
    
    print("\n" + "=" * 70)
    print("📊 RESULTS")
    print("=" * 70)
    
    if len(working_endpoints) > 1:
        print(f"✅ Found {len(working_endpoints)} working endpoints for load balancing!")
        print("\nWorking endpoints:")
        for i, endpoint in enumerate(working_endpoints, 1):
            print(f"  {i}. {endpoint['name']}")
            print(f"     {endpoint['endpoint']}")
        
        print(f"\n💡 You can use these {len(working_endpoints)} endpoints for load balancing!")
        
    elif len(working_endpoints) == 1:
        print("⚠️ Only 1 working endpoint found.")
        print("This will work but may be slower for concurrent requests.")
        print(f"Working: {working_endpoints[0]['name']}")
        
    else:
        print("❌ No working endpoints found!")
    
    return working_endpoints

if __name__ == "__main__":
    working = main()
    
    if len(working) > 1:
        print(f"\n🚀 RECOMMENDATION: Update LLM_ENDPOINTS with {len(working)} working endpoints")
    elif len(working) == 1:
        print(f"\n⚠️ RECOMMENDATION: Continue with single endpoint, monitor for additional keys")
    else:
        print(f"\n🚨 RECOMMENDATION: Contact Azure support for API key renewal")
