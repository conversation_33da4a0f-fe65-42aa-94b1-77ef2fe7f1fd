#!/usr/bin/env python3
"""
Test script to verify the Excel generation fix for Unicode characters.
"""

import os
import sys
import django

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing, Symbol, Segment
from api.utils import generate_measurement_excel

def test_unicode_characters():
    """Test Unicode character handling in Excel generation."""
    
    # Test data with problematic Unicode characters
    test_symbols = [
        'â‚¬20.7',      # Should become Ø20.7
        'â‚¬19-23',     # Should become Ø19-23  
        'R0.45',
        '45Â°',         # Should become 45°
        '0.2',
        '0.3R',
        'â‚¬22.08',     # Should become Ø22.08
        'â‚¬1.40Â±0.05', # Should become Ø1.40±0.05
        '175P.C.D.',
        '0.1',
        '0.2',
        'Â°38',         # Should become °38
        '0.85+0.04-0.04',
        '0.60-0.16',
        'â‚¬15.1Â±0.04', # Should become Ø15.1±0.04
    ]
    
    print("Testing Unicode character replacement:")
    for symbol_value in test_symbols:
        # Clean and normalize Unicode characters for better Excel compatibility
        cleaned_value = str(symbol_value)
        cleaned_value = cleaned_value.replace('\u00e2\u0082\u00ac', '\u00d8')  # Fix diameter symbol (â‚¬ -> Ø)
        cleaned_value = cleaned_value.replace('\u00c2\u00b1', '\u00b1')        # Fix plus-minus symbol (Â± -> ±)
        cleaned_value = cleaned_value.replace('\u00c2\u00b0', '\u00b0')        # Fix degree symbol (Â° -> °)
        cleaned_value = cleaned_value.replace('\u00e2\u0088\u0092', '-')       # Fix minus symbol (âˆ' -> -)
        
        print(f"Original: '{symbol_value}' -> Cleaned: '{cleaned_value}'")

def test_excel_generation():
    """Test Excel generation with a real drawing if available."""
    try:
        # Try to find a drawing with symbols
        drawing = Drawing.objects.filter(
            segments__symbols__is_marked=True
        ).distinct().first()
        
        if drawing:
            print(f"\nTesting Excel generation for drawing ID: {drawing.id}")
            excel_path = generate_measurement_excel(drawing)
            print(f"Excel file generated: {excel_path}")
            
            # Check if file exists
            full_path = os.path.join('/home/<USER>/manu/drawing_analyzer/media', excel_path)
            if os.path.exists(full_path):
                print(f"✓ Excel file exists at: {full_path}")
                print(f"File size: {os.path.getsize(full_path)} bytes")
            else:
                print(f"✗ Excel file not found at: {full_path}")
        else:
            print("No drawings with marked symbols found for testing")
            
    except Exception as e:
        print(f"Error during Excel generation test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 60)
    print("EXCEL UNICODE FIX TEST")
    print("=" * 60)
    
    test_unicode_characters()
    test_excel_generation()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETED")
    print("=" * 60)
