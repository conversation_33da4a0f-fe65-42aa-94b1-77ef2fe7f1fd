#!/usr/bin/env python3
"""
Test script to verify font improvements in bubble generation.
This script creates a test image with different bubble sizes to verify
that the font is now larger and more visible.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from PIL import Image, ImageDraw, ImageFont
from django.conf import settings
import time

def test_font_improvements():
    """Test the improved font rendering with different bubble sizes"""
    
    # Create a test image
    width, height = 800, 600
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # Test different bubble sizes
    bubble_sizes = [30, 50, 70, 100]
    
    y_position = 100
    
    for bubble_size in bubble_sizes:
        x_position = 150
        
        # Calculate font size using our new formula
        font_size = max(20, int(bubble_size * 1.0))
        
        # Try to load the font using our new font loading logic
        font = None
        try:
            # Try to load Arial Bold first for better visibility
            font = ImageFont.truetype("arialbd.ttf", font_size)
            font_name = "Arial Bold"
        except IOError:
            try:
                # Try regular Arial
                font = ImageFont.truetype("arial.ttf", font_size)
                font_name = "Arial"
            except IOError:
                try:
                    # Try system fonts - Helvetica Bold
                    font = ImageFont.truetype("/System/Library/Fonts/Helvetica-Bold.ttc", font_size)
                    font_name = "Helvetica Bold"
                except IOError:
                    try:
                        # Try regular Helvetica
                        font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
                        font_name = "Helvetica"
                    except IOError:
                        font = ImageFont.load_default()
                        font_name = "Default"
        
        # Draw the bubble
        circle_radius = int(bubble_size / 2)
        
        # Draw outer circle in blue with fill
        draw.ellipse(
            [(x_position - circle_radius, y_position - circle_radius),
             (x_position + circle_radius, y_position + circle_radius)],
            fill="blue",
            outline="blue",
            width=max(1, int(bubble_size * 0.05))
        )
        
        # Draw inner circle with white outline
        inner_radius = int(circle_radius * 0.9)
        draw.ellipse(
            [(x_position - inner_radius, y_position - inner_radius),
             (x_position + inner_radius, y_position + inner_radius)],
            fill="blue",
            outline="white",
            width=max(1, int(bubble_size * 0.02))
        )
        
        # Draw the number with stroke effect
        test_number = "42"
        
        # Get text dimensions
        try:
            left, top, right, bottom = draw.textbbox((0, 0), test_number, font=font)
            text_width = right - left
            text_height = bottom - top
        except AttributeError:
            try:
                text_width, text_height = draw.textsize(test_number, font=font)
            except:
                text_width, text_height = font_size // 2, font_size // 2
        
        # Calculate text position
        text_x = x_position - text_width/2
        text_y = y_position - text_height/2
        
        # Draw text stroke (outline) in black for better contrast
        stroke_width = 2
        for dx in range(-stroke_width, stroke_width + 1):
            for dy in range(-stroke_width, stroke_width + 1):
                if dx != 0 or dy != 0:  # Don't draw at the center position
                    draw.text(
                        (text_x + dx, text_y + dy),
                        test_number,
                        fill="black",
                        font=font
                    )
        
        # Draw the main white text on top
        draw.text(
            (text_x, text_y),
            test_number,
            fill="white",
            font=font
        )
        
        # Add labels
        label_x = x_position + circle_radius + 20
        draw.text(
            (label_x, y_position - 10),
            f"Bubble Size: {bubble_size}px",
            fill="black",
            font=ImageFont.load_default()
        )
        draw.text(
            (label_x, y_position + 10),
            f"Font Size: {font_size}px ({font_name})",
            fill="black",
            font=ImageFont.load_default()
        )
        
        y_position += 120
    
    # Add title
    title_font = None
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
    except:
        title_font = ImageFont.load_default()
    
    draw.text(
        (50, 30),
        "Font Improvement Test - Process B Finalized Drawing Bubbles",
        fill="black",
        font=title_font
    )
    
    # Save the test image
    timestamp = int(time.time())
    output_path = os.path.join(settings.MEDIA_ROOT, f"font_test_{timestamp}.png")
    img.save(output_path)
    
    print(f"Font improvement test image saved to: {output_path}")
    print("This image shows the improved font rendering with:")
    print("- Larger font sizes (1.0x bubble size instead of 0.6x)")
    print("- Bold fonts when available")
    print("- Text stroke/outline for better visibility")
    print("- Blue filled bubbles for better contrast")
    
    return output_path

if __name__ == "__main__":
    test_font_improvements()
