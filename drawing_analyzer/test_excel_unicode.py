#!/usr/bin/env python3
"""
Simple test to create an Excel file with Unicode characters to verify the fix.
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import os

def create_test_excel():
    """Create a test Excel file with Unicode characters."""
    
    # Create a new workbook and select the active worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Unicode Test"

    # Define column headers
    headers = ["S.No.", "Measurement", "Min Value", "Max Value", "Instrument Value (Yes/No)", "Remarks"]

    # Set column headers with styling
    header_fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
    header_font = Font(bold=True, name='Cal<PERSON>ri', size=11)
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_idx, value=header)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = thin_border

    # Test data with Unicode characters
    test_data = [
        (1, "Ø20.7", "Ø20.7", "Ø20.7", "Yes", ""),
        (2, "Ø19-23", "Ø19-23", "Ø19-23", "Yes", ""),
        (3, "R0.45", "0.45", "0.45", "Yes", ""),
        (4, "45°", "45°", "45°", "Yes", ""),
        (5, "0.2", "0.2", "0.2", "Yes", ""),
        (6, "0.3R", "0.3R", "0.3R", "Yes", ""),
        (7, "Ø22.08", "Ø22.08", "Ø22.08", "Yes", ""),
        (8, "Ø1.40±0.05", "1.35", "1.45", "Yes", ""),
        (9, "175P.C.D.", "175P.C.D.", "175P.C.D.", "Yes", ""),
        (10, "0.1", "0.1", "0.1", "Yes", ""),
        (11, "0.2", "0.2", "0.2", "Yes", ""),
        (12, "°38", "38", "38", "Yes", ""),
        (13, "0.85+0.04-0.04", "0.81", "0.89", "Yes", ""),
        (14, "0.60-0.16", "0.44", "0.6", "Yes", ""),
        (15, "Ø15.1±0.04", "15.06", "15.14", "Yes", ""),
    ]

    # Add test data
    cell_font = Font(name='Calibri', size=10)
    center_alignment = Alignment(horizontal='center', vertical='center')
    
    for row_idx, (s_no, measurement, min_val, max_val, instrument, remarks) in enumerate(test_data, 2):
        # Add data to cells
        s_no_cell = ws.cell(row=row_idx, column=1, value=s_no)
        measurement_cell = ws.cell(row=row_idx, column=2, value=measurement)
        min_cell = ws.cell(row=row_idx, column=3, value=min_val)
        max_cell = ws.cell(row=row_idx, column=4, value=max_val)
        instrument_cell = ws.cell(row=row_idx, column=5, value=instrument)
        remarks_cell = ws.cell(row=row_idx, column=6, value=remarks)
        
        # Apply formatting
        for cell in [s_no_cell, measurement_cell, min_cell, max_cell, instrument_cell, remarks_cell]:
            cell.font = cell_font
            cell.border = thin_border
            
        # Center align numeric columns
        s_no_cell.alignment = center_alignment
        min_cell.alignment = center_alignment
        max_cell.alignment = center_alignment
        instrument_cell.alignment = center_alignment

    # Auto-adjust column widths
    column_widths = [8, 20, 12, 12, 18, 15]
    for col_idx, width in enumerate(column_widths, 1):
        column_letter = get_column_letter(col_idx)
        ws.column_dimensions[column_letter].width = width

    # Save the workbook
    excel_path = os.path.join('/home/<USER>/manu/drawing_analyzer/media', 'unicode_test.xlsx')
    
    try:
        wb.save(excel_path)
        print(f"✓ Excel file created successfully: {excel_path}")
        
        # Check file size
        if os.path.exists(excel_path):
            file_size = os.path.getsize(excel_path)
            print(f"✓ File size: {file_size} bytes")
        
        return excel_path
    except Exception as e:
        print(f"✗ Error creating Excel file: {e}")
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("UNICODE EXCEL TEST")
    print("=" * 60)
    
    excel_file = create_test_excel()
    
    if excel_file:
        print(f"\nTest Excel file created: {excel_file}")
        print("Please download and open this file in Excel on Windows to verify Unicode characters display correctly.")
    else:
        print("\nFailed to create test Excel file.")
    
    print("\n" + "=" * 60)
    print("TEST COMPLETED")
    print("=" * 60)
