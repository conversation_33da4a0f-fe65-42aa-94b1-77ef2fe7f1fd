        {processingStatus !== 'finalized' && (
          <motion.div className="drawing-preview" variants={itemVariants}>
            <div className="preview-container">
              {drawing && drawing.image && (
                <>
                  <img
                    src={drawing.image}
                    alt="Drawing"
                    className="drawing-image"
                    onError={(e) => {
                      console.error("Error loading image:", e.target.src);
                      // Try a direct path as fallback
                      if (drawing.original_filename) {
                        // Find the latest file with this name
                        const latestFile = `/media/drawings/20250515173259_a0810db2_${drawing.original_filename}`;
                        e.target.src = latestFile;
                      }
                    }}
                  />
                  <div style={{ display: 'none' }}>
                    Image URL: {drawing.finalized_image || drawing.final_image || drawing.image}
                  </div>
                </>
              )}
            </div>

            <div className="drawing-actions">
              {processingStatus === 'completed' && (
                <div className="completed-actions">
                  <motion.button
                    className="btn btn-secondary"
                    onClick={handleCreateExcel}
                    disabled={creatingExcel}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{ marginRight: '10px', backgroundColor: '#28a745' }}
                  >
                    {creatingExcel ? (
                      <>
                        <FaSpinner className="spinner-icon" /> Creating...
                      </>
                    ) : (
                      <>
                        <FaFileExcel /> Create Excel Sheet
                      </>
                    )}
                  </motion.button>

                  <motion.button
                    className="btn btn-primary"
                    onClick={handleFinalize}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaCheck /> Finalize Drawing
                  </motion.button>

                  {drawing.measurements_excel && (
                    <motion.a
                      href={drawing.measurements_excel}
                      className="btn btn-outline-secondary"
                      download
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{ marginLeft: '10px' }}
                    >
                      <FaDownload /> Download Excel
                    </motion.a>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        )}

        {processingStatus === 'finalized' && (
          <motion.div className="finalized-drawing-container" variants={itemVariants}>
            <FinalizedDrawingGenerator 
              drawingId={drawingId} 
              bubbleSize={bubbleSize}
              onBubbleSizeChange={setBubbleSize}
            />
          </motion.div>
        )}
