#!/usr/bin/env python3
"""
Direct test of enhanced OCR functionality without API calls
"""

import os
import sys
import django

# Add the drawing_analyzer directory to Python path
sys.path.append('/home/<USER>/manu/drawing_analyzer')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'drawing_analyzer.settings')
django.setup()

from api.models import Drawing, Segment, BoundingBox
from PIL import Image
import easyocr

def test_enhanced_ocr_parameters():
    """Test the enhanced OCR parameters directly"""
    print("🧪 Testing Enhanced OCR Parameters")
    print("=" * 50)
    
    # Get a Process B drawing
    process_b_drawings = Drawing.objects.filter(process_type='B')
    if not process_b_drawings.exists():
        print("❌ No Process B drawings found")
        return False
    
    drawing = process_b_drawings.first()
    print(f"✅ Using drawing: {drawing.original_filename} (ID: {drawing.id})")
    
    # Get segments
    segments = Segment.objects.filter(drawing=drawing)
    if not segments.exists():
        print("❌ No segments found")
        return False
    
    print(f"✅ Found {segments.count()} segments")
    
    # Find a small segment or use the first one
    small_segment = None
    for seg in segments:
        if os.path.exists(seg.image.path):
            with Image.open(seg.image.path) as test_img:
                width, height = test_img.size
                max_dimension = max(width, height)
                if max_dimension < 800:
                    small_segment = seg
                    break

    # Use small segment if found, otherwise use first segment
    segment = small_segment if small_segment else segments.first()
    print(f"🔍 Testing segment {segment.segment_number}")
    
    # Check if segment image exists
    if not os.path.exists(segment.image.path):
        print(f"❌ Segment image not found: {segment.image.path}")
        return False
    
    # Load image and check size
    with Image.open(segment.image.path) as img:
        width, height = img.size
        max_dimension = max(width, height)
        is_small_segment = max_dimension < 800
        
        print(f"📏 Segment size: {width}x{height}")
        print(f"📏 Max dimension: {max_dimension}")
        print(f"🔍 Is small segment: {is_small_segment}")
        
        # Convert to RGB if needed
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Convert PIL image to numpy array for EasyOCR
        import numpy as np
        img_array = np.array(img)

        # Initialize EasyOCR
        reader = easyocr.Reader(['en'])

        # Test standard parameters
        print("\n🔧 Testing standard OCR parameters...")
        standard_results = reader.readtext(img_array)
        print(f"📊 Standard OCR detected: {len(standard_results)} text regions")
        
        # Test enhanced parameters for small segments
        if is_small_segment:
            print("\n🚀 Testing enhanced parameters for small segment...")
            enhanced_results = reader.readtext(
                img_array,
                # Enhanced parameters for small segments
                min_size=1,
                text_threshold=0.1,
                low_text=0.02,
                link_threshold=0.2,
                canvas_size=4096,
                mag_ratio=2.0,
                bbox_min_score=0.02,
                bbox_min_size=1,
                contrast_ths=0.01,
                adjust_contrast=0.1,
                filter_ths=0.0001,
                slope_ths=0.05,
                ycenter_ths=0.8,
                height_ths=0.6,
                width_ths=0.8,
                allowlist='0123456789.,±°ØøRMxABCDEFGHIJKLMNOPQRSTUVWXYZ+-=()[]{}|/\\<>~`!@#$%^&*_:;"\' μ'
            )
            print(f"📊 Enhanced OCR detected: {len(enhanced_results)} text regions")
            
            # Compare results
            print(f"\n📈 Improvement: {len(enhanced_results) - len(standard_results)} additional detections")
            
            # Show new detections
            standard_texts = {result[1] for result in standard_results}
            enhanced_texts = {result[1] for result in enhanced_results}
            new_detections = enhanced_texts - standard_texts
            
            if new_detections:
                print(f"🆕 New detections with enhanced parameters:")
                for text in new_detections:
                    print(f"   - '{text}'")
            else:
                print("ℹ️ No new detections found")
                
            # Show all enhanced detections with confidence
            print(f"\n🔍 All enhanced OCR detections:")
            for i, (bbox, text, confidence) in enumerate(enhanced_results):
                print(f"   {i+1}. '{text}' (confidence: {confidence:.3f})")
        else:
            print("ℹ️ Segment is not small, enhanced parameters not needed")

            # Test enhanced parameters on a cropped version to simulate small segment
            print("\n🔬 Testing enhanced parameters on cropped version (simulating small segment)...")

            # Crop to a small area (e.g., 600x400 pixels from center)
            crop_width, crop_height = 600, 400
            left = (width - crop_width) // 2
            top = (height - crop_height) // 2
            right = left + crop_width
            bottom = top + crop_height

            cropped_img = img.crop((left, top, right, bottom))
            cropped_array = np.array(cropped_img)

            print(f"📏 Cropped size: {crop_width}x{crop_height}")

            # Test standard parameters on cropped image
            cropped_standard_results = reader.readtext(cropped_array)
            print(f"📊 Standard OCR on cropped: {len(cropped_standard_results)} text regions")

            # Test enhanced parameters on cropped image
            cropped_enhanced_results = reader.readtext(
                cropped_array,
                # Enhanced parameters for small segments
                min_size=1,
                text_threshold=0.1,
                low_text=0.02,
                link_threshold=0.2,
                canvas_size=4096,
                mag_ratio=2.0,
                bbox_min_score=0.02,
                bbox_min_size=1,
                contrast_ths=0.01,
                adjust_contrast=0.1,
                filter_ths=0.0001,
                slope_ths=0.05,
                ycenter_ths=0.8,
                height_ths=0.6,
                width_ths=0.8,
                allowlist='0123456789.,±°ØøRMxABCDEFGHIJKLMNOPQRSTUVWXYZ+-=()[]{}|/\\<>~`!@#$%^&*_:;"\' μ'
            )
            print(f"📊 Enhanced OCR on cropped: {len(cropped_enhanced_results)} text regions")

            # Compare results
            improvement = len(cropped_enhanced_results) - len(cropped_standard_results)
            print(f"📈 Improvement on cropped: {improvement} additional detections")

            # Show cropped detections
            if cropped_enhanced_results:
                print(f"\n🔍 Enhanced OCR detections on cropped segment:")
                for i, (bbox, text, confidence) in enumerate(cropped_enhanced_results):
                    print(f"   {i+1}. '{text}' (confidence: {confidence:.3f})")
        
        # Show all standard detections for comparison
        print(f"\n🔍 All standard OCR detections:")
        for i, (bbox, text, confidence) in enumerate(standard_results):
            print(f"   {i+1}. '{text}' (confidence: {confidence:.3f})")
    
    return True

def test_mu_character_detection():
    """Test if μ (mu) character is being detected"""
    print("\n🔬 Testing μ (mu) character detection")
    print("=" * 40)
    
    # Test text containing μ
    test_texts = [
        "6μm/mm",
        "Permissible slope: 6μm/mm max",
        "μ",
        "0.27 max μR",
    ]
    
    reader = easyocr.Reader(['en'])
    
    for test_text in test_texts:
        print(f"Testing detection of: '{test_text}'")
        # This would require creating a test image with the text
        # For now, just verify the allowlist includes μ
        allowlist = '0123456789.,±°ØøRMxABCDEFGHIJKLMNOPQRSTUVWXYZ+-=()[]{}|/\\<>~`!@#$%^&*_:;"\' μ'
        if 'μ' in allowlist:
            print(f"   ✅ μ character is in allowlist")
        else:
            print(f"   ❌ μ character missing from allowlist")

if __name__ == "__main__":
    print("🚀 Starting Enhanced OCR Direct Tests")
    print("=" * 60)
    
    try:
        # Test 1: Enhanced OCR parameters
        success = test_enhanced_ocr_parameters()
        
        # Test 2: μ character detection
        test_mu_character_detection()
        
        if success:
            print(f"\n🎉 Enhanced OCR tests completed!")
            print(f"💡 The enhanced parameters should improve detection on small segments")
            print(f"💡 Look for additional detections with lower confidence thresholds")
        else:
            print(f"\n❌ Some tests failed")
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        import traceback
        traceback.print_exc()
