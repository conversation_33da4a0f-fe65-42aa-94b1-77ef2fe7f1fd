#!/usr/bin/env python3

# Read the file
with open('frontend/src/pages/ResultsPage.jsx', 'r') as f:
    lines = f.readlines()

# Find the line with the problematic style attribute
for i, line in enumerate(lines):
    if 'className="drawing-preview"' in line and 'style={{display:' in line:
        # Replace this line and the section
        lines[i] = '        {processingStatus !== \'finalized\' && (\n'
        break

# Find the line where we need to close the conditional and add the finalized section
for i, line in enumerate(lines):
    if 'processingStatus === \'finalized\' && (' in line:
        # Insert the closing bracket before this line
        lines.insert(i, '        )}\n\n')
        # Update the finalized section
        lines[i+1] = '        {processingStatus === \'finalized\' && (\n'
        # Find the end of FinalizedDrawingGenerator and close it properly
        for j in range(i+2, len(lines)):
            if '</motion.div>' in lines[j] and 'finalized' not in lines[j-5:j]:
                lines[j] = '          </motion.div>\n        )}\n'
                break
        break

# Write the file back
with open('frontend/src/pages/ResultsPage.jsx', 'w') as f:
    f.writelines(lines)

print("File fixed successfully!")
