import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCheck, FaTimes, FaEdit, FaRobot, FaEye, FaMerge, FaSeparate } from 'react-icons/fa';
import './EnhancedConfirmationDialog.scss';

const EnhancedConfirmationDialog = ({
  isOpen,
  symbols,
  spatialAnalysis,
  automationRecommendation,
  onConfirm,
  onCancel,
  onAutomate
}) => {
  const [userDecisions, setUserDecisions] = useState([]);
  const [mergeMode, setMergeMode] = useState(false);
  const [selectedSymbols, setSelectedSymbols] = useState(new Set());
  const [processingStartTime] = useState(Date.now());

  // Initialize user decisions when symbols change
  useEffect(() => {
    if (symbols && symbols.length > 0) {
      const initialDecisions = symbols.map((symbol, index) => ({
        index,
        accepted: true,
        final_value: symbol.value,
        symbol_type: symbol.symbol_type,
        edited: false,
        rejection_reason: null
      }));
      setUserDecisions(initialDecisions);
    }
  }, [symbols]);

  // Handle individual symbol decision
  const handleSymbolDecision = (index, decision) => {
    setUserDecisions(prev => prev.map((d, i) => 
      i === index ? { ...d, ...decision } : d
    ));
  };

  // Handle merge selection
  const toggleSymbolSelection = (index) => {
    setSelectedSymbols(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  // Merge selected symbols
  const handleMergeSelected = () => {
    if (selectedSymbols.size < 2) return;

    const selectedIndices = Array.from(selectedSymbols).sort();
    const mergedValue = selectedIndices
      .map(i => symbols[i].value)
      .join(' ');

    // Update the first selected symbol with merged value
    const firstIndex = selectedIndices[0];
    handleSymbolDecision(firstIndex, {
      final_value: mergedValue,
      edited: true
    });

    // Mark other selected symbols as rejected
    selectedIndices.slice(1).forEach(index => {
      handleSymbolDecision(index, {
        accepted: false,
        rejection_reason: 'MERGED_WITH_OTHERS'
      });
    });

    setMergeMode(false);
    setSelectedSymbols(new Set());
  };

  // Get decision type based on user actions
  const getDecisionType = () => {
    const acceptedCount = userDecisions.filter(d => d.accepted).length;
    const totalCount = userDecisions.length;
    
    if (acceptedCount === 0) return 'REJECT_ALL';
    if (acceptedCount === 1 && totalCount === 1) return 'ACCEPT_SINGLE';
    if (acceptedCount === totalCount) return 'MERGE_ALL';
    return 'MERGE_PARTIAL';
  };

  // Handle final confirmation
  const handleFinalConfirm = () => {
    const processingTime = (Date.now() - processingStartTime) / 1000;
    
    const confirmationData = {
      symbols: symbols,
      spatial_analysis: spatialAnalysis,
      user_decisions: userDecisions,
      decision_type: getDecisionType(),
      processing_time: processingTime,
      merge_pattern: {
        merged_symbols: userDecisions
          .map((d, i) => d.accepted ? i : null)
          .filter(i => i !== null)
      }
    };

    onConfirm(confirmationData);
  };

  // Handle automation
  const handleAutomate = () => {
    if (automationRecommendation?.should_automate) {
      onAutomate(automationRecommendation);
    }
  };

  if (!isOpen || !symbols || symbols.length === 0) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="enhanced-confirmation-overlay"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <motion.div
          className="enhanced-confirmation-dialog"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
        >
          {/* Header */}
          <div className="dialog-header">
            <h3>
              <FaRobot /> Symbol Detection Results
            </h3>
            <div className="symbol-count">
              {symbols.length} symbol{symbols.length !== 1 ? 's' : ''} detected
            </div>
          </div>

          {/* Spatial Analysis Summary */}
          {spatialAnalysis && (
            <div className="spatial-analysis">
              <h4>Spatial Analysis</h4>
              <div className="analysis-grid">
                <div className="analysis-item">
                  <span className="label">Same Line:</span>
                  <span className={`value ${spatialAnalysis.symbols_on_same_line ? 'positive' : 'negative'}`}>
                    {spatialAnalysis.symbols_on_same_line ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="analysis-item">
                  <span className="label">Alignment:</span>
                  <span className="value">{spatialAnalysis.horizontal_alignment}</span>
                </div>
                <div className="analysis-item">
                  <span className="label">Grouping:</span>
                  <span className="value">{spatialAnalysis.symbol_grouping}</span>
                </div>
                <div className="analysis-item">
                  <span className="label">Recommended:</span>
                  <span className="value recommended">{spatialAnalysis.recommended_action}</span>
                </div>
              </div>
              {spatialAnalysis.reasoning && (
                <div className="reasoning">
                  <strong>Reasoning:</strong> {spatialAnalysis.reasoning}
                </div>
              )}
            </div>
          )}

          {/* Automation Recommendation */}
          {automationRecommendation && automationRecommendation.should_automate && (
            <div className="automation-recommendation">
              <div className="recommendation-header">
                <FaRobot /> Automation Available
              </div>
              <div className="recommendation-content">
                <p><strong>Action:</strong> {automationRecommendation.recommended_action}</p>
                <p><strong>Confidence:</strong> {(automationRecommendation.confidence * 100).toFixed(1)}%</p>
                <p><strong>Reasoning:</strong> {automationRecommendation.reasoning}</p>
              </div>
              <button 
                className="btn btn-success"
                onClick={handleAutomate}
              >
                <FaRobot /> Apply Automation
              </button>
            </div>
          )}

          {/* Symbol List */}
          <div className="symbols-list">
            <div className="list-header">
              <h4>Detected Symbols</h4>
              <div className="merge-controls">
                <button
                  className={`btn btn-sm ${mergeMode ? 'btn-warning' : 'btn-outline-secondary'}`}
                  onClick={() => setMergeMode(!mergeMode)}
                >
                  {mergeMode ? <FaSeparate /> : <FaMerge />}
                  {mergeMode ? 'Cancel Merge' : 'Merge Mode'}
                </button>
                {mergeMode && selectedSymbols.size >= 2 && (
                  <button
                    className="btn btn-sm btn-primary"
                    onClick={handleMergeSelected}
                  >
                    <FaMerge /> Merge Selected ({selectedSymbols.size})
                  </button>
                )}
              </div>
            </div>

            {symbols.map((symbol, index) => {
              const decision = userDecisions[index] || {};
              return (
                <div 
                  key={index} 
                  className={`symbol-item ${!decision.accepted ? 'rejected' : ''} ${mergeMode && selectedSymbols.has(index) ? 'selected' : ''}`}
                >
                  {mergeMode && (
                    <div className="merge-checkbox">
                      <input
                        type="checkbox"
                        checked={selectedSymbols.has(index)}
                        onChange={() => toggleSymbolSelection(index)}
                      />
                    </div>
                  )}
                  
                  <div className="symbol-info">
                    <div className="symbol-value">
                      <input
                        type="text"
                        value={decision.final_value || symbol.value}
                        onChange={(e) => handleSymbolDecision(index, {
                          final_value: e.target.value,
                          edited: e.target.value !== symbol.value
                        })}
                        className="value-input"
                      />
                    </div>
                    <div className="symbol-type">
                      <select
                        value={decision.symbol_type || symbol.symbol_type}
                        onChange={(e) => handleSymbolDecision(index, {
                          symbol_type: e.target.value
                        })}
                        className="type-select"
                      >
                        <option value="diameter">Diameter</option>
                        <option value="radius">Radius</option>
                        <option value="tolerance">Tolerance</option>
                        <option value="dimension">Dimension</option>
                        <option value="degree">Degree</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div className="symbol-actions">
                    <button
                      className={`btn btn-sm ${decision.accepted ? 'btn-success' : 'btn-outline-success'}`}
                      onClick={() => handleSymbolDecision(index, { accepted: true })}
                    >
                      <FaCheck />
                    </button>
                    <button
                      className={`btn btn-sm ${!decision.accepted ? 'btn-danger' : 'btn-outline-danger'}`}
                      onClick={() => handleSymbolDecision(index, { 
                        accepted: false,
                        rejection_reason: 'USER_PREFERENCE'
                      })}
                    >
                      <FaTimes />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Footer Actions */}
          <div className="dialog-footer">
            <button className="btn btn-secondary" onClick={onCancel}>
              Cancel
            </button>
            <button 
              className="btn btn-primary"
              onClick={handleFinalConfirm}
            >
              Confirm Decisions ({userDecisions.filter(d => d.accepted).length} symbols)
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default EnhancedConfirmationDialog;
