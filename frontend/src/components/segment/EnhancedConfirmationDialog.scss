.enhanced-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.enhanced-confirmation-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  
  .dialog-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;
      
      svg {
        color: #007bff;
      }
    }
    
    .symbol-count {
      background: #007bff;
      color: white;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }
  
  .spatial-analysis {
    padding: 16px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    
    h4 {
      margin: 0 0 12px 0;
      color: #495057;
      font-size: 1rem;
    }
    
    .analysis-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      margin-bottom: 12px;
    }
    
    .analysis-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .label {
        font-weight: 500;
        color: #6c757d;
        font-size: 0.875rem;
      }
      
      .value {
        font-weight: 600;
        font-size: 0.875rem;
        
        &.positive {
          color: #28a745;
        }
        
        &.negative {
          color: #dc3545;
        }
        
        &.recommended {
          color: #007bff;
          text-transform: capitalize;
        }
      }
    }
    
    .reasoning {
      font-size: 0.875rem;
      color: #6c757d;
      font-style: italic;
      margin-top: 8px;
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      border-left: 3px solid #007bff;
    }
  }
  
  .automation-recommendation {
    padding: 16px 24px;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-bottom: 1px solid #e9ecef;
    
    .recommendation-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #28a745;
      margin-bottom: 12px;
      
      svg {
        font-size: 1.1rem;
      }
    }
    
    .recommendation-content {
      margin-bottom: 16px;
      
      p {
        margin: 4px 0;
        font-size: 0.875rem;
        color: #495057;
      }
    }
    
    .btn {
      font-size: 0.875rem;
      padding: 8px 16px;
    }
  }
  
  .symbols-list {
    padding: 20px 24px;
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        color: #495057;
      }
      
      .merge-controls {
        display: flex;
        gap: 8px;
        
        .btn {
          font-size: 0.875rem;
          padding: 6px 12px;
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
    
    .symbol-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      margin-bottom: 8px;
      transition: all 0.2s ease;
      
      &:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
      }
      
      &.rejected {
        background: #f8f9fa;
        opacity: 0.7;
        border-color: #dc3545;
      }
      
      &.selected {
        background: #e3f2fd;
        border-color: #2196f3;
      }
      
      .merge-checkbox {
        input[type="checkbox"] {
          width: 18px;
          height: 18px;
          cursor: pointer;
        }
      }
      
      .symbol-info {
        flex: 1;
        display: flex;
        gap: 12px;
        align-items: center;
        
        .symbol-value {
          flex: 2;
          
          .value-input {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.875rem;
            
            &:focus {
              outline: none;
              border-color: #007bff;
              box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }
          }
        }
        
        .symbol-type {
          flex: 1;
          
          .type-select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.875rem;
            background: white;
            
            &:focus {
              outline: none;
              border-color: #007bff;
              box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }
          }
        }
      }
      
      .symbol-actions {
        display: flex;
        gap: 6px;
        
        .btn {
          padding: 6px 10px;
          font-size: 0.875rem;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 36px;
          
          svg {
            font-size: 0.875rem;
          }
        }
      }
    }
  }
  
  .dialog-footer {
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    
    .btn {
      padding: 10px 20px;
      font-weight: 500;
      border-radius: 6px;
      
      &.btn-primary {
        background: #007bff;
        border-color: #007bff;
        
        &:hover {
          background: #0056b3;
          border-color: #0056b3;
        }
      }
      
      &.btn-secondary {
        background: #6c757d;
        border-color: #6c757d;
        color: white;
        
        &:hover {
          background: #545b62;
          border-color: #545b62;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .enhanced-confirmation-dialog {
    margin: 10px;
    max-height: 95vh;
    
    .dialog-header {
      padding: 16px 20px 12px;
      
      h3 {
        font-size: 1.1rem;
      }
      
      .symbol-count {
        font-size: 0.8rem;
        padding: 3px 8px;
      }
    }
    
    .spatial-analysis {
      padding: 12px 20px;
      
      .analysis-grid {
        grid-template-columns: 1fr;
        gap: 8px;
      }
    }
    
    .symbols-list {
      padding: 16px 20px;
      
      .list-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        
        .merge-controls {
          width: 100%;
          justify-content: flex-start;
        }
      }
      
      .symbol-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        
        .symbol-info {
          flex-direction: column;
          gap: 8px;
        }
        
        .symbol-actions {
          justify-content: center;
        }
      }
    }
    
    .dialog-footer {
      padding: 12px 20px;
      flex-direction: column;
      
      .btn {
        width: 100%;
      }
    }
  }
}
