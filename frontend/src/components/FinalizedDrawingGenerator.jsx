import React, { useState, useRef, useEffect } from 'react';
import { FaDownload, FaSpinner, FaImage, FaCircle, FaRuler, FaFileExcel } from 'react-icons/fa';
import { motion } from 'framer-motion';
import axios from 'axios';
import './FinalizedDrawingGenerator.scss';

const FinalizedDrawingGenerator = ({ drawingId, bubbleSize: initialBubbleSize = 60, onBubbleSizeChange }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [drawing, setDrawing] = useState(null);
  const [allSymbols, setAllSymbols] = useState([]);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [bubbleSize, setBubbleSize] = useState(initialBubbleSize);
  const [adjustingBubbles, setAdjustingBubbles] = useState(false);
  
  const containerRef = useRef(null);
  const imageRef = useRef(null);

  // Sync bubble size with parent
  useEffect(() => {
    setBubbleSize(initialBubbleSize);
  }, [initialBubbleSize]);

  // Fetch drawing and all symbols data
  useEffect(() => {
    const fetchData = async () => {
      if (!drawingId) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch drawing details
        const drawingResponse = await axios.get(`/api/results/${drawingId}/`);
        setDrawing(drawingResponse.data.drawing);

        // Fetch all segments and their symbols
        const segments = drawingResponse.data.segments || [];
        const allSymbolsData = [];
        let symbolCounter = 1;

        // Process segments in the correct order (2, 1, 3, 4, ...)
        const segmentOrder = [];
        
        // First, try to find segments with numbers 2, 1, 3, 4
        for (const segmentNumber of [2, 1, 3, 4]) {
          const segment = segments.find(s => s.segment_number === segmentNumber);
          if (segment) {
            segmentOrder.push(segment);
          }
        }
        
        // Add any remaining segments in their natural order
        for (const segment of segments) {
          if (!segmentOrder.includes(segment)) {
            segmentOrder.push(segment);
          }
        }

        // Process each segment in the specified order
        for (const segment of segmentOrder) {
          try {
            const segmentResponse = await axios.get(`/api/segment/${segment.id}/`);
            const symbols = segmentResponse.data.symbols || [];
            
            // Filter only marked symbols and add segment offset
            const markedSymbols = symbols
              .filter(symbol => symbol.is_marked)
              .map(symbol => ({
                ...symbol,
                // Calculate absolute position in the full drawing
                absoluteX: (segment.segment_x_offset || 0) + symbol.x_coordinate,
                absoluteY: (segment.segment_y_offset || 0) + symbol.y_coordinate,
                sequentialNumber: symbolCounter++,
                segmentNumber: segment.segment_number
              }));
            
            allSymbolsData.push(...markedSymbols);
          } catch (err) {
            console.error(`Error fetching segment ${segment.id}:`, err);
          }
        }

        setAllSymbols(allSymbolsData);
        console.log(`Loaded ${allSymbolsData.length} symbols for finalized drawing`);
        console.log('Drawing data:', drawingResponse.data.drawing);
        console.log('Excel file:', drawingResponse.data.drawing.measurements_excel);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load drawing data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [drawingId]);

  // Handle image load
  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  // Handle bubble size change
  const handleBubbleSizeChange = (e) => {
    const newSize = parseInt(e.target.value);
    setBubbleSize(newSize);
    if (onBubbleSizeChange) {
      onBubbleSizeChange(newSize);
    }
  };

  // Apply bubble size (for backend compatibility)
  const applyBubbleSize = async () => {
    try {
      setAdjustingBubbles(true);
      const response = await axios.post(`/api/adjust-bubbles/${drawingId}/`, {
        bubble_size: bubbleSize
      });
      // Update the drawing with the new image URL if provided
      if (response.data.final_image) {
        setDrawing(prev => ({
          ...prev,
          finalized_image: response.data.final_image,
          final_image: response.data.final_image
        }));
      }
      setAdjustingBubbles(false);
    } catch (err) {
      console.error('Error adjusting bubble size:', err);
      setAdjustingBubbles(false);
    }
  };

  // Generate and download the finalized image
  const generateFinalizedImage = async () => {
    if (!imageLoaded || !imageRef.current || !containerRef.current) {
      setError('Image not ready for generation');
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      const image = imageRef.current;
      
      // Create canvas with the same dimensions as the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size to match the original image dimensions
      canvas.width = image.naturalWidth;
      canvas.height = image.naturalHeight;
      
      // Calculate scale factors
      const scaleX = image.naturalWidth / image.offsetWidth;
      const scaleY = image.naturalHeight / image.offsetHeight;
      
      // Draw the original image
      ctx.drawImage(image, 0, 0);
      
      // Draw bubbles with perfect text rendering
      allSymbols.forEach(symbol => {
        const x = symbol.absoluteX * scaleX;
        const y = symbol.absoluteY * scaleY;
        const radius = (bubbleSize / 2) * scaleX;
        
        // Draw outer circle (blue background)
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, 2 * Math.PI);
        ctx.fillStyle = '#007bff';
        ctx.fill();
        ctx.strokeStyle = '#0056b3';
        ctx.lineWidth = Math.max(1, bubbleSize * 0.05 * scaleX);
        ctx.stroke();
        
        // Draw inner circle with white outline
        const innerRadius = radius * 0.9;
        ctx.beginPath();
        ctx.arc(x, y, innerRadius, 0, 2 * Math.PI);
        ctx.fillStyle = '#007bff';
        ctx.fill();
        ctx.strokeStyle = 'white';
        ctx.lineWidth = Math.max(1, bubbleSize * 0.02 * scaleX);
        ctx.stroke();
        
        // Draw text with large, bold font
        const fontSize = Math.max(20, bubbleSize * 1.2 * scaleX);
        ctx.font = `bold ${fontSize}px Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // Draw text stroke (black outline) for better visibility
        const strokeWidth = Math.max(1, bubbleSize * 0.02 * scaleX);
        ctx.strokeStyle = 'black';
        ctx.lineWidth = strokeWidth * 2;
        ctx.strokeText(symbol.sequentialNumber.toString(), x, y);
        
        // Draw main text (white)
        ctx.fillStyle = 'white';
        ctx.fillText(symbol.sequentialNumber.toString(), x, y);
      });
      
      // Convert canvas to blob and download
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `finalized_drawing_${drawingId}_bubbles_${bubbleSize}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        setGenerating(false);
      }, 'image/png', 1.0);
      
    } catch (err) {
      console.error('Error generating finalized image:', err);
      setError('Failed to generate finalized image');
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="finalized-generator-loading">
        <FaSpinner className="spinner" />
        <span>Loading drawing data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="finalized-generator-error">
        <span>{error}</span>
      </div>
    );
  }

  if (!drawing) {
    return null;
  }


  return (
    <div className="finalized-drawing-generator">
      <div className="generator-header">
        <h3>🎯 High-Quality Finalized Drawing Generator</h3>
        <p>Generate finalized drawing with perfect bubble text visibility using frontend rendering</p>
      </div>
      
      <div 
        ref={containerRef}
        className="drawing-preview-container"
        style={{ position: 'relative', display: 'inline-block' }}
      >
        <img
          ref={imageRef}
          src={drawing.image}
          alt="Original Drawing"
          className="preview-image"
          onLoad={handleImageLoad}
          style={{ maxWidth: '100%', height: 'auto' }}
        />
        
        {/* Render frontend bubbles as overlay - these show perfect text visibility */}
        {imageLoaded && allSymbols.map(symbol => {
          const image = imageRef.current;
          if (!image) return null;
          
          const scaleX = image.offsetWidth / image.naturalWidth;
          const scaleY = image.offsetHeight / image.naturalHeight;
          
          const displayX = symbol.absoluteX * scaleX;
          const displayY = symbol.absoluteY * scaleY;
          
          const markerSize = bubbleSize;
          const fontSize = Math.max(12, Math.floor(markerSize * 0.6));
          
          return (
            <div
              key={`preview-bubble-${symbol.id}`}
              className="preview-bubble"
              style={{
                position: 'absolute',
                left: `${displayX}px`,
                top: `${displayY}px`,
                width: `${markerSize}px`,
                height: `${markerSize}px`,
                backgroundColor: '#007bff',
                border: '2px solid white',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: `${fontSize}px`,
                transform: 'translate(-50%, -50%)',
                zIndex: 10,
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
              }}
            >
              {symbol.sequentialNumber}
            </div>
          );
        })}
      </div>
      
      {/* Bubble Size Control */}
      <div className="bubble-size-control">
        <div className="bubble-size-header">
          <label htmlFor="bubbleSize"><FaCircle className="bubble-icon" /> Bubble Size</label>
          <div className="bubble-size-value">
            <motion.div
              className="bubble-preview"
              animate={{
                width: `${Math.max(20, Math.min(40, bubbleSize / 3))}px`,
                height: `${Math.max(20, Math.min(40, bubbleSize / 3))}px`
              }}
            >
              <span>{bubbleSize}</span>
            </motion.div>
          </div>
        </div>
        <div className="bubble-size-slider-container">
          <input
            type="range"
            id="bubbleSize"
            min="20"
            max="150"
            value={bubbleSize}
            onChange={handleBubbleSizeChange}
            className="bubble-size-slider"
          />
          <motion.button
            className="btn btn-apply"
            onClick={applyBubbleSize}
            disabled={adjustingBubbles}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {adjustingBubbles ? (
              <><FaSpinner className="spinner-icon" /> Applying...</>
            ) : (
              <><FaRuler /> Apply Size</>
            )}
          </motion.button>
        </div>
      </div>

      {/* Download Actions */}
      <div className="download-actions">
        <motion.button
          className="btn btn-primary"
          onClick={generateFinalizedImage}
          disabled={!imageLoaded || generating || allSymbols.length === 0}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {generating ? (
            <>
              <FaSpinner className="spinner" />
              Generating...
            </>
          ) : (
            <>
              <FaImage />
              Download High-Quality Image with Bubbles
            </>
          )}
        </motion.button>

        {drawing && drawing.measurements_excel && (
          <motion.a
            href={drawing.measurements_excel}
            className="btn btn-secondary"
            download
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{ marginLeft: '10px', backgroundColor: '#28a745' }}
          >
            <FaFileExcel /> Download Measurements Excel
          </motion.a>
        )}

        {drawing && drawing.summary_file && (
          <motion.a
            href={drawing.summary_file}
            className="btn btn-secondary"
            download
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{ marginLeft: '10px' }}
          >
            <FaDownload /> Download Summary
          </motion.a>
        )}

        <div className="symbol-count">
          {allSymbols.length} symbols will be numbered
        </div>
      </div>
    </div>
  );
};

export default FinalizedDrawingGenerator;
