import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FaImage, FaDownload, FaCheck, FaSpinner, FaExclamationTriangle, FaCircle, FaRuler, FaFileExcel } from 'react-icons/fa';
import axios from 'axios';
import './ResultsPage.scss';
import FinalizedDrawingGenerator from '../components/FinalizedDrawingGenerator';

const ResultsPage = () => {
  const { drawingId } = useParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [drawing, setDrawing] = useState(null);
  const [segments, setSegments] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [processingStatus, setProcessingStatus] = useState('processing');
  const [logs, setLogs] = useState([]);
  const [bubbleSize, setBubbleSize] = useState(60);
  const [adjustingBubbles, setAdjustingBubbles] = useState(false);
  const [creatingExcel, setCreatingExcel] = useState(false);

  // Fetch drawing data
  useEffect(() => {
    // Only fetch data if we have a valid drawingId
    if (!drawingId || drawingId === 'null' || drawingId === 'undefined') {
      setError('No drawing ID provided. Redirecting to upload page...');
      setLoading(false);
      // Redirect to upload page after a short delay
      setTimeout(() => {
        navigate('/upload');
      }, 2000);
      return () => {}; // Return empty cleanup function
    }

    const fetchData = async () => {
      try {
        const response = await axios.get(`/api/results/${drawingId}/`);
        console.log('Drawing data:', response.data.drawing);
        console.log('Image URL:', response.data.drawing.image);
        setDrawing(response.data.drawing);
        setSegments(response.data.segments);
        setProcessingStatus(response.data.drawing.processed ? 'completed' : 'processing');
        setLoading(false);
      } catch (err) {
        console.error('Error fetching results:', err);
        setError('Failed to load results. Please try again.');
        setLoading(false);
      }
    };

    fetchData();

    // Poll for updates if processing
    const intervalId = setInterval(async () => {
      // Skip polling if no drawingId
      if (!drawingId) return;

      try {
        // Check processing status
        const statusResponse = await axios.get(`/api/results/${drawingId}/`);
        const isProcessed = statusResponse.data.drawing.processed;

        if (isProcessed) {
          setDrawing(statusResponse.data.drawing);
          setSegments(statusResponse.data.segments);
          setProcessingStatus('completed');
          clearInterval(intervalId);
        }

        // Get logs
        const logsResponse = await axios.get(`/api/log/${drawingId}/`);
        setLogs(logsResponse.data.logs);
      } catch (err) {
        console.error('Error polling status:', err);
      }
    }, 3000);

    return () => clearInterval(intervalId);
  }, [drawingId]);

  // Handle creating Excel sheet (without finalizing)
  const handleCreateExcel = async () => {
    try {
      setCreatingExcel(true);

      const response = await axios.post(`/api/create-excel/${drawingId}/`);

      // Update the drawing with the Excel file URL
      setDrawing({
        ...drawing,
        measurements_excel: response.data.measurements_excel
      });

      // Show success message or notification
      console.log('Excel sheet created successfully');
    } catch (err) {
      console.error('Error creating Excel sheet:', err);
      setError('Failed to create Excel sheet. Please try again.');
    } finally {
      setCreatingExcel(false);
    }
  };

  // Handle finalize
  const handleFinalize = async () => {
    try {
      setProcessingStatus('finalizing');

      const response = await axios.post(`/api/finalize/${drawingId}/`);

      setDrawing({
        ...drawing,
        finalized: true,
        finalized_image: response.data.final_image,
        final_image: response.data.final_image,
        measurements_excel: response.data.measurements_excel,
        summary_file: response.data.summary_file
      });

      setProcessingStatus('finalized');
    } catch (err) {
      console.error('Error finalizing drawing:', err);
      setError('Failed to finalize drawing. Please try again.');
      setProcessingStatus('completed');
    }
  };

  // Handle bubble size adjustment
  const handleBubbleSizeChange = (e) => {
    setBubbleSize(parseInt(e.target.value));
  };

  // Apply bubble size adjustment
  const applyBubbleSize = async () => {
    try {
      setAdjustingBubbles(true);

      const response = await axios.post(`/api/adjust-bubbles/${drawingId}/`, {
        bubble_size: bubbleSize
      });

      // Update the drawing with the new image URL if provided
      if (response.data.final_image) {
        setDrawing({
          ...drawing,
          finalized_image: response.data.final_image,
          final_image: response.data.final_image
        });
      }

      setAdjustingBubbles(false);
    } catch (err) {
      console.error('Error adjusting bubble size:', err);
      setError('Failed to adjust bubble size. Please try again.');
      setAdjustingBubbles(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <p>Loading results...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <FaExclamationTriangle />
        <h2>Error</h2>
        <p>{error}</p>
        <div className="error-actions">
          <button
            className="btn btn-primary"
            onClick={() => navigate('/upload')}
          >
            Go to Upload
          </button>
          <button
            className="btn btn-secondary"
            onClick={() => {
              setError(null);
              setLoading(true);
              // Try to fetch data again if we have a drawing ID
              if (drawingId && drawingId !== 'null' && drawingId !== 'undefined') {
                axios.get(`/api/results/${drawingId}/`)
                  .then(response => {
                    setDrawing(response.data.drawing);
                    setSegments(response.data.segments);
                    setProcessingStatus(response.data.drawing.processed ? 'completed' : 'processing');
                    setLoading(false);
                  })
                  .catch(err => {
                    console.error('Error retrying fetch:', err);
                    setError('Failed to load results. Please try again.');
                    setLoading(false);
                  });
              } else {
                setLoading(false);
                setError('No drawing ID provided. Redirecting to upload page...');
                // Redirect to upload page after a short delay
                setTimeout(() => {
                  navigate('/upload');
                }, 2000);
              }
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="results-page"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="container">
        <motion.div className="results-header" variants={itemVariants}>
          <h1>Analysis Results</h1>
          <div className="status-badge">
            {processingStatus === 'processing' && (
              <span className="badge processing">
                <FaSpinner className="spinner-icon" /> Processing
              </span>
            )}
            {processingStatus === 'completed' && (
              <span className="badge completed">
                <FaCheck /> Completed
              </span>
            )}
            {processingStatus === 'finalizing' && (
              <span className="badge processing">
                <FaSpinner className="spinner-icon" /> Finalizing
              </span>
            )}
            {processingStatus === 'finalized' && (
              <span className="badge finalized">
                <FaCheck /> Finalized
              </span>
            )}
          </div>
        </motion.div>

        <motion.div className="drawing-preview" variants={itemVariants}>
          <div className="preview-container">
            {drawing && drawing.image && (
              <>
                <img
                  src={processingStatus === 'finalized' ? (drawing.finalized_image || drawing.final_image) : drawing.image}
                  alt="Drawing"
                  className="drawing-image"
                  onError={(e) => {
                    console.error("Error loading image:", e.target.src);
                    // Try a direct path as fallback
                    if (drawing.original_filename) {
                      // Find the latest file with this name
                      const latestFile = `/media/drawings/20250515173259_a0810db2_${drawing.original_filename}`;
                      e.target.src = latestFile;
                    }
                  }}
                />
                <div style={{ display: 'none' }}>
                  Image URL: {drawing.finalized_image || drawing.final_image || drawing.image}
                </div>
              </>
            )}
          </div>

          <div className="drawing-actions">
            {processingStatus === 'completed' && (
              <div className="completed-actions">
                <motion.button
                  className="btn btn-secondary"
                  onClick={handleCreateExcel}
                  disabled={creatingExcel}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{ marginRight: '10px', backgroundColor: '#28a745' }}
                >
                  {creatingExcel ? (
                    <>
                      <FaSpinner className="spinner-icon" /> Creating...
                    </>
                  ) : (
                    <>
                      <FaFileExcel /> Create Excel Sheet
                    </>
                  )}
                </motion.button>

                <motion.button
                  className="btn btn-primary"
                  onClick={handleFinalize}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FaCheck /> Finalize Drawing
                </motion.button>

                {drawing.measurements_excel && (
                  <motion.a
                    href={drawing.measurements_excel}
                    className="btn btn-outline-secondary"
                    download
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{ marginLeft: '10px' }}
                  >
                    <FaDownload /> Download Excel
                  </motion.a>
                )}
              </div>
            )}

            {processingStatus === 'finalized' && (
              <>
                <div className="bubble-size-control">
                  <div className="bubble-size-header">
                    <label htmlFor="bubbleSize"><FaCircle className="bubble-icon" /> Bubble Size</label>
                    <div className="bubble-size-value">
                      <motion.div
                        className="bubble-preview"
                        animate={{
                          width: `${Math.max(20, Math.min(40, bubbleSize / 3))}px`,
                          height: `${Math.max(20, Math.min(40, bubbleSize / 3))}px`
                        }}
                      >
                        <span>{bubbleSize}</span>
                      </motion.div>
                    </div>
                  </div>
                  <div className="bubble-size-slider-container">
                    <input
                      type="range"
                      id="bubbleSize"
                      min="20"
                      max="150"
                      value={bubbleSize}
                      onChange={handleBubbleSizeChange}
                      className="bubble-size-slider"
                    />
                    <motion.button
                      className="btn btn-apply"
                      onClick={applyBubbleSize}
                      disabled={adjustingBubbles}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {adjustingBubbles ? (
                        <><FaSpinner className="spinner-icon" /> Applying...</>
                      ) : (
                        <><FaRuler /> Apply Size</>
                      )}
                    </motion.button>
                  </div>
                </div>

                <div className="download-actions">
                  <motion.a
                    href={drawing.finalized_image || drawing.final_image}
                    className="btn btn-primary"
                    download
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaImage /> Download Image with Numbered Bubbles
                  </motion.a>

                  <motion.a
                    href={drawing.measurements_excel}
                    className="btn btn-secondary"
                    download
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{ marginLeft: '10px', backgroundColor: '#28a745' }}
                  >
                    <FaDownload /> Download Measurements Excel
                  </motion.a>

                  {drawing.summary_file && (
                    <motion.a
                      href={drawing.summary_file}
                      className="btn btn-secondary"
                      download
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaDownload /> Download Summary
                    </motion.a>
                  )}
                </div>
              </>
            )}

        {/* Frontend Finalized Drawing Generator */}
        {processingStatus === 'finalized' && (
          <motion.div className="frontend-generator-section" variants={itemVariants}>
            <FinalizedDrawingGenerator 
              drawingId={drawingId} 
              bubbleSize={bubbleSize} 
            />
          </motion.div>
        )}
          </div>
        </motion.div>

        <motion.div className="results-tabs" variants={itemVariants}>
          <div className="tabs-header">
            <button
              className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
              onClick={() => setActiveTab('overview')}
            >
              Overview
            </button>
            <button
              className={`tab-btn ${activeTab === 'segments' ? 'active' : ''}`}
              onClick={() => setActiveTab('segments')}
            >
              Segments
            </button>
            <button
              className={`tab-btn ${activeTab === 'logs' ? 'active' : ''}`}
              onClick={() => setActiveTab('logs')}
            >
              Logs
            </button>
          </div>

          <div className="tabs-content">
            <AnimatePresence mode="wait">
              {activeTab === 'overview' && (
                <motion.div
                  key="overview"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="tab-content overview-tab"
                >
                  <div className="overview-info">
                    <div className="info-item">
                      <span className="label">File Name:</span>
                      <span className="value">{drawing.original_filename}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">Uploaded:</span>
                      <span className="value">{new Date(drawing.uploaded_at).toLocaleString()}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">Status:</span>
                      <span className="value">{drawing.processed ? 'Processed' : 'Processing'}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">Segments:</span>
                      <span className="value">{segments.length}</span>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'segments' && (
                <motion.div
                  key="segments"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="tab-content segments-tab"
                >
                  <div className="segments-grid">
                    {segments.map((segment) => (
                      <motion.div
                        key={segment.id}
                        className="segment-card"
                        whileHover={{ scale: 1.03, boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.15)" }}
                      >
                        <Link to={`/segment/${segment.id}`}>
                          <div className="segment-image-container">
                            <img
                              src={segment.marked_image || segment.image}
                              alt={`Segment ${segment.segment_number}`}
                              className="segment-image"
                            />
                          </div>
                          <div className="segment-info">
                            <h3>Segment {segment.segment_number}</h3>
                            <p>{segment.detected_symbols_count || 0} symbols detected</p>
                          </div>
                        </Link>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              )}

              {activeTab === 'logs' && (
                <motion.div
                  key="logs"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="tab-content logs-tab"
                >
                  <div className="logs-container">
                    {logs.map((log, index) => (
                      <div key={index} className="log-entry">
                        <span className="log-time">{new Date(log.timestamp).toLocaleTimeString()}</span>
                        <span className="log-message">{log.message}</span>
                      </div>
                    ))}

                    {logs.length === 0 && (
                      <div className="no-logs">
                        <p>No logs available</p>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ResultsPage;
